import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service.dart';

/// Widget that displays character profiles before the story begins
class CharacterProfilesWidget extends StatefulWidget {
  final EnhancedStoryModel story;
  final VoidCallback onContinue;
  final StoryNarrationService narrationService;

  const CharacterProfilesWidget({
    super.key,
    required this.story,
    required this.onContinue,
    required this.narrationService,
  });

  @override
  State<CharacterProfilesWidget> createState() => _CharacterProfilesWidgetState();
}

class _CharacterProfilesWidgetState extends State<CharacterProfilesWidget>
    with TickerProviderStateMixin {
  late final AnimationController _staggerController;
  late final PageController _pageController;
  
  int _currentCharacterIndex = 0;
  bool _isNarrating = false;
  List<AnimationController> _characterControllers = [];
  List<Animation<double>> _characterAnimations = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _pageController = PageController();
    _startCharacterIntroductions();
  }

  void _initializeAnimations() {
    _staggerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Create animations for each character
    for (int i = 0; i < widget.story.characters.length; i++) {
      final controller = AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      );
      
      final animation = Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Interval(
          i * 0.2,
          1.0,
          curve: Curves.easeOutBack,
        ),
      ));

      _characterControllers.add(controller);
      _characterAnimations.add(animation);
    }
  }

  Future<void> _startCharacterIntroductions() async {
    // Start stagger animation
    _staggerController.forward();
    
    // Animate characters one by one
    for (int i = 0; i < _characterControllers.length; i++) {
      await Future.delayed(Duration(milliseconds: i * 300));
      _characterControllers[i].forward();
    }
  }

  Future<void> _narrateCharacter(CharacterModel character) async {
    if (_isNarrating) return;

    setState(() {
      _isNarrating = true;
    });

    try {
      // For now, we'll simulate narration
      // In a full implementation, you would use the narration service
      await Future.delayed(const Duration(seconds: 2));
    } catch (e) {
      // Handle narration error
    } finally {
      setState(() {
        _isNarrating = false;
      });
    }
  }

  void _nextCharacter() {
    if (_currentCharacterIndex < widget.story.characters.length - 1) {
      setState(() {
        _currentCharacterIndex++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousCharacter() {
    if (_currentCharacterIndex > 0) {
      setState(() {
        _currentCharacterIndex--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void dispose() {
    _staggerController.dispose();
    _pageController.dispose();
    for (final controller in _characterControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primaryContainer.withOpacity(0.3),
            theme.colorScheme.surface,
            theme.colorScheme.secondaryContainer.withOpacity(0.2),
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Header
            Padding(
              padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
              child: Column(
                children: [
                  Text(
                    'Meet the Characters',
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                      fontSize: isSmallScreen ? 24 : 28,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Get to know the friends in your story!',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),

            // Character profiles
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentCharacterIndex = index;
                  });
                },
                itemCount: widget.story.characters.length,
                itemBuilder: (context, index) {
                  final character = widget.story.characters[index];
                  return AnimatedBuilder(
                    animation: _characterAnimations[index],
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _characterAnimations[index].value,
                        child: Opacity(
                          opacity: _characterAnimations[index].value.clamp(0.0, 1.0),
                          child: _buildCharacterCard(character, theme, isSmallScreen),
                        ),
                      );
                    },
                  );
                },
              ),
            ),

            // Navigation and controls
            Padding(
              padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
              child: Column(
                children: [
                  // Page indicator
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      widget.story.characters.length,
                      (index) => Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: index == _currentCharacterIndex
                              ? theme.colorScheme.primary
                              : theme.colorScheme.outline.withValues(alpha: 0.3),
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Navigation buttons
                  Row(
                    children: [
                      if (_currentCharacterIndex > 0)
                        Expanded(
                          child: OutlinedButton(
                            onPressed: _previousCharacter,
                            child: const Text('Previous'),
                          ),
                        ),
                      
                      if (_currentCharacterIndex > 0 && _currentCharacterIndex < widget.story.characters.length - 1)
                        const SizedBox(width: 16),
                      
                      if (_currentCharacterIndex < widget.story.characters.length - 1)
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _nextCharacter,
                            child: const Text('Next'),
                          ),
                        ),
                      
                      if (_currentCharacterIndex == widget.story.characters.length - 1)
                        Expanded(
                          child: ElevatedButton(
                            onPressed: widget.onContinue,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: theme.colorScheme.primary,
                              foregroundColor: theme.colorScheme.onPrimary,
                            ),
                            child: const Text('Start Story'),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCharacterCard(CharacterModel character, ThemeData theme, bool isSmallScreen) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 16.0 : 32.0,
        vertical: 16.0,
      ),
      child: Card(
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: EdgeInsets.all(isSmallScreen ? 20.0 : 32.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.surface,
                theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Character image
              Container(
                width: isSmallScreen ? 100 : 120,
                height: isSmallScreen ? 100 : 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipOval(
                  child: Image.asset(
                    character.getImagePath(widget.story.storyId),
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: theme.colorScheme.primaryContainer,
                        child: Icon(
                          Icons.person,
                          size: isSmallScreen ? 50 : 60,
                          color: theme.colorScheme.primary,
                        ),
                      );
                    },
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Character name
              Text(
                character.name,
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                  fontSize: isSmallScreen ? 20 : 24,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 8),
              
              // Character role
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: theme.colorScheme.secondaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  character.role,
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: theme.colorScheme.onSecondaryContainer,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Character description
              Text(
                character.description,
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontSize: isSmallScreen ? 16 : 18,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 24),
              
              // Narrate button
              OutlinedButton.icon(
                onPressed: _isNarrating ? null : () => _narrateCharacter(character),
                icon: _isNarrating 
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.volume_up),
                label: Text(_isNarrating ? 'Speaking...' : 'Hear About ${character.name}'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
