import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/services/progress_tracking_service.dart';
import 'package:choice_once_upon_a_time/models/progress_tracking_models.dart';

/// Provider for the progress tracking service
final progressTrackingServiceProvider = Provider<ProgressTrackingService>((ref) {
  return ProgressTrackingService.instance;
});

/// Provider for current reading progress
final currentProgressProvider = StreamProvider<ReadingProgress?>((ref) {
  final service = ref.watch(progressTrackingServiceProvider);
  return service.progressStream;
});

/// Provider for reading sessions
final readingSessionsProvider = StreamProvider<List<ReadingSession>>((ref) {
  final service = ref.watch(progressTrackingServiceProvider);
  return service.sessionsStream;
});

/// Provider for reading streak
final readingStreakProvider = StreamProvider<ReadingStreak?>((ref) {
  final service = ref.watch(progressTrackingServiceProvider);
  return service.streakStream;
});

/// Provider for achievements
final achievementsProvider = StreamProvider<List<Achievement>>((ref) {
  final service = ref.watch(progressTrackingServiceProvider);
  return service.achievementsStream;
});

/// Provider for weekly summary data
final weeklySummaryProvider = Provider<WeeklySummary?>((ref) {
  final sessionsAsync = ref.watch(readingSessionsProvider);
  
  return sessionsAsync.when(
    data: (sessions) => _calculateWeeklySummary(sessions),
    loading: () => null,
    error: (_, __) => null,
  );
});

/// Provider for monthly summary data
final monthlySummaryProvider = Provider<MonthlySummary?>((ref) {
  final sessionsAsync = ref.watch(readingSessionsProvider);
  
  return sessionsAsync.when(
    data: (sessions) => _calculateMonthlySummary(sessions),
    loading: () => null,
    error: (_, __) => null,
  );
});

/// Provider for daily progress data (last 7 days)
final dailyProgressProvider = Provider<List<DailyProgressData>>((ref) {
  final sessionsAsync = ref.watch(readingSessionsProvider);
  
  return sessionsAsync.when(
    data: (sessions) => _calculateDailyProgress(sessions),
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider for progress statistics
final progressStatsProvider = Provider<ProgressStats?>((ref) {
  final progressAsync = ref.watch(currentProgressProvider);
  final streakAsync = ref.watch(readingStreakProvider);
  final achievementsAsync = ref.watch(achievementsProvider);
  
  return progressAsync.when(
    data: (progress) {
      if (progress == null) return null;
      
      final streak = streakAsync.value;
      final achievements = achievementsAsync.value ?? [];
      
      return ProgressStats(
        totalReadingTime: progress.totalReadingTime,
        storiesCompleted: progress.storiesCompleted,
        totalSessions: progress.totalSessions,
        averageSessionTime: progress.averageSessionTime,
        currentStreak: streak?.currentStreak ?? 0,
        longestStreak: streak?.longestStreak ?? 0,
        achievementsUnlocked: achievements.length,
        favoriteGenre: progress.favoriteGenre,
      );
    },
    loading: () => null,
    error: (_, __) => null,
  );
});

/// Calculate weekly summary from sessions
WeeklySummary _calculateWeeklySummary(List<ReadingSession> sessions) {
  final now = DateTime.now();
  final weekStart = now.subtract(Duration(days: now.weekday - 1));
  final weekEnd = weekStart.add(const Duration(days: 6));
  
  final weekSessions = sessions.where((session) {
    final sessionDate = DateTime(
      session.startTime.year,
      session.startTime.month,
      session.startTime.day,
    );
    return sessionDate.isAfter(weekStart.subtract(const Duration(days: 1))) &&
           sessionDate.isBefore(weekEnd.add(const Duration(days: 1)));
  }).toList();
  
  final totalMinutes = weekSessions.fold(0, (sum, session) => sum + session.durationMinutes);
  final storiesCompleted = weekSessions.where((s) => s.completed).length;
  final sessionsCount = weekSessions.length;
  
  // Calculate daily data for the week
  final dailyData = <DailyProgressData>[];
  for (int i = 0; i < 7; i++) {
    final date = weekStart.add(Duration(days: i));
    final daySessions = weekSessions.where((session) {
      final sessionDate = DateTime(
        session.startTime.year,
        session.startTime.month,
        session.startTime.day,
      );
      return sessionDate.isAtSameMomentAs(date);
    }).toList();
    
    dailyData.add(DailyProgressData(
      date: date,
      minutesRead: daySessions.fold(0, (sum, session) => sum + session.durationMinutes),
      storiesCompleted: daySessions.where((s) => s.completed).length,
      sessionsCount: daySessions.length,
    ));
  }
  
  return WeeklySummary(
    weekStart: weekStart,
    weekEnd: weekEnd,
    totalMinutes: totalMinutes,
    storiesCompleted: storiesCompleted,
    sessionsCount: sessionsCount,
    dailyData: dailyData,
  );
}

/// Calculate monthly summary from sessions
MonthlySummary _calculateMonthlySummary(List<ReadingSession> sessions) {
  final now = DateTime.now();
  final monthStart = DateTime(now.year, now.month, 1);
  final monthEnd = DateTime(now.year, now.month + 1, 0);
  
  final monthSessions = sessions.where((session) {
    final sessionDate = DateTime(
      session.startTime.year,
      session.startTime.month,
      session.startTime.day,
    );
    return sessionDate.isAfter(monthStart.subtract(const Duration(days: 1))) &&
           sessionDate.isBefore(monthEnd.add(const Duration(days: 1)));
  }).toList();
  
  final totalMinutes = monthSessions.fold(0, (sum, session) => sum + session.durationMinutes);
  final storiesCompleted = monthSessions.where((s) => s.completed).length;
  final sessionsCount = monthSessions.length;
  
  // Calculate weekly data for the month
  final weeklyData = <WeeklySummary>[];
  DateTime currentWeekStart = monthStart;
  
  while (currentWeekStart.isBefore(monthEnd)) {
    final weekEnd = currentWeekStart.add(const Duration(days: 6));
    final weekSessions = monthSessions.where((session) {
      final sessionDate = DateTime(
        session.startTime.year,
        session.startTime.month,
        session.startTime.day,
      );
      return sessionDate.isAfter(currentWeekStart.subtract(const Duration(days: 1))) &&
             sessionDate.isBefore(weekEnd.add(const Duration(days: 1)));
    }).toList();
    
    final weekMinutes = weekSessions.fold(0, (sum, session) => sum + session.durationMinutes);
    final weekStoriesCompleted = weekSessions.where((s) => s.completed).length;
    final weekSessionsCount = weekSessions.length;
    
    // Calculate daily data for this week
    final dailyData = <DailyProgressData>[];
    for (int i = 0; i < 7; i++) {
      final date = currentWeekStart.add(Duration(days: i));
      if (date.isAfter(monthEnd)) break;
      
      final daySessions = weekSessions.where((session) {
        final sessionDate = DateTime(
          session.startTime.year,
          session.startTime.month,
          session.startTime.day,
        );
        return sessionDate.isAtSameMomentAs(date);
      }).toList();
      
      dailyData.add(DailyProgressData(
        date: date,
        minutesRead: daySessions.fold(0, (sum, session) => sum + session.durationMinutes),
        storiesCompleted: daySessions.where((s) => s.completed).length,
        sessionsCount: daySessions.length,
      ));
    }
    
    weeklyData.add(WeeklySummary(
      weekStart: currentWeekStart,
      weekEnd: weekEnd,
      totalMinutes: weekMinutes,
      storiesCompleted: weekStoriesCompleted,
      sessionsCount: weekSessionsCount,
      dailyData: dailyData,
    ));
    
    currentWeekStart = currentWeekStart.add(const Duration(days: 7));
  }
  
  return MonthlySummary(
    monthStart: monthStart,
    monthEnd: monthEnd,
    totalMinutes: totalMinutes,
    storiesCompleted: storiesCompleted,
    sessionsCount: sessionsCount,
    weeklyData: weeklyData,
  );
}

/// Calculate daily progress for the last 7 days
List<DailyProgressData> _calculateDailyProgress(List<ReadingSession> sessions) {
  final now = DateTime.now();
  final dailyData = <DailyProgressData>[];
  
  for (int i = 6; i >= 0; i--) {
    final date = DateTime(now.year, now.month, now.day).subtract(Duration(days: i));
    final daySessions = sessions.where((session) {
      final sessionDate = DateTime(
        session.startTime.year,
        session.startTime.month,
        session.startTime.day,
      );
      return sessionDate.isAtSameMomentAs(date);
    }).toList();
    
    dailyData.add(DailyProgressData(
      date: date,
      minutesRead: daySessions.fold(0, (sum, session) => sum + session.durationMinutes),
      storiesCompleted: daySessions.where((s) => s.completed).length,
      sessionsCount: daySessions.length,
    ));
  }
  
  return dailyData;
}

/// Model for progress statistics
class ProgressStats {
  final int totalReadingTime;
  final int storiesCompleted;
  final int totalSessions;
  final double averageSessionTime;
  final int currentStreak;
  final int longestStreak;
  final int achievementsUnlocked;
  final String favoriteGenre;

  const ProgressStats({
    required this.totalReadingTime,
    required this.storiesCompleted,
    required this.totalSessions,
    required this.averageSessionTime,
    required this.currentStreak,
    required this.longestStreak,
    required this.achievementsUnlocked,
    required this.favoriteGenre,
  });
}
