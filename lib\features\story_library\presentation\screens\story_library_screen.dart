import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/providers/story_library_provider.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/story_cover_card_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/screens/narrated_story_player_screen.dart';
import 'package:choice_once_upon_a_time/app/providers/settings_provider.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator_widget.dart';

/// A dedicated screen to display the entire story library with search and filtering.
class StoryLibraryScreen extends ConsumerStatefulWidget {
  const StoryLibraryScreen({super.key});

  @override
  ConsumerState<StoryLibraryScreen> createState() => _StoryLibraryScreenState();
}

class _StoryLibraryScreenState extends ConsumerState<StoryLibraryScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      ref.read(storyLibraryProvider.notifier).search(_searchController.text);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onStoryTap(BuildContext context, StoryMetadataModel story) {
    // Check if this is an enhanced story (story001, story002, or any with enhanced_asset dataSource)
    if (story.dataSource == 'enhanced_asset' ||
        story.id == 'story001' ||
        story.id == 'story002') {
      context.go('/enhanced_story_player/${story.id}');
    } else {
      context.go('/story/${story.id}?dataSource=${story.dataSource}');
    }
  }

  Future<void> _onRefresh() async {
    // This calls the robust refresh method in our provider.
    await ref.read(storyLibraryProvider.notifier).refresh();
  }

  @override
  Widget build(BuildContext context) {
    final storyLibraryState = ref.watch(storyLibraryProvider);
    final filteredStories = ref.watch(filteredStoriesProvider);
    final languageCode = ref.watch(narrationLanguageProvider);
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Full Story Library',
          style: theme.textTheme.titleLarge?.copyWith(
            fontSize: isSmallScreen ? 18 : 20,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/home'),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Responsive search bar
            Padding(
            padding: EdgeInsets.symmetric(
              horizontal: screenSize.width * 0.04, // 4% of screen width
              vertical: isSmallScreen ? 12.0 : 16.0,
            ),
            child: TextField(
              controller: _searchController,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontSize: isSmallScreen ? 14 : 16,
              ),
              decoration: InputDecoration(
                hintText: 'Search stories by title...',
                hintStyle: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  fontSize: isSmallScreen ? 14 : 16,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: theme.colorScheme.onSurfaceVariant,
                  size: isSmallScreen ? 20 : 24,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.0),
                  borderSide: BorderSide(color: theme.colorScheme.outline),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.0),
                  borderSide: BorderSide(color: theme.colorScheme.outline),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.0),
                  borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
                ),
                filled: true,
                fillColor: theme.colorScheme.surfaceContainerHighest,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 12 : 16,
                  vertical: isSmallScreen ? 12 : 16,
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(
                          Icons.clear,
                          color: theme.colorScheme.onSurfaceVariant,
                          size: isSmallScreen ? 20 : 24,
                        ),
                        onPressed: () {
                          _searchController.clear();
                        },
                        tooltip: 'Clear search',
                      )
                    : null,
              ),
            ),
          ),
          Expanded(
            child: _buildBody(context, storyLibraryState, filteredStories, languageCode),
          ),
        ],
        ),
      ),
    );
  }

  Widget _buildBody(
    BuildContext context,
    StoryLibraryState state,
    List<StoryMetadataModel> stories,
    String languageCode,
  ) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    if (state.isLoading && stories.isEmpty) {
      return const Center(child: LoadingIndicatorWidget());
    }

    if (state.error != null && stories.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(screenSize.width * 0.08),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: isSmallScreen ? 48 : 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Error: ${state.error}',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.error,
                  fontSize: isSmallScreen ? 14 : 16,
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      );
    }

    // Wrap the GridView with RefreshIndicator
    return RefreshIndicator(
      onRefresh: _onRefresh,
      color: theme.colorScheme.primary,
      child: LayoutBuilder(
        builder: (context, constraints) {
          if (stories.isEmpty) {
            // Handle empty state within the scrollable area for pull-to-refresh to work
            return SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(minHeight: constraints.maxHeight),
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.all(screenSize.width * 0.08),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: isSmallScreen ? 48 : 64,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          state.searchQuery.isNotEmpty
                              ? 'No stories match your search.'
                              : 'No stories available.',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: isSmallScreen ? 16 : 18,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (state.searchQuery.isNotEmpty) ...[
                          const SizedBox(height: 8),
                          Text(
                            'Try adjusting your search terms',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                              fontSize: isSmallScreen ? 12 : 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            );
          }

          final screenWidth = constraints.maxWidth;
          int crossAxisCount;
          double childAspectRatio;
          double spacing;

          if (screenWidth > 1200) {
            crossAxisCount = 5;
            childAspectRatio = 0.8;
            spacing = 20.0;
          } else if (screenWidth > 800) {
            crossAxisCount = 4;
            childAspectRatio = 0.75;
            spacing = 18.0;
          } else if (screenWidth > 500) {
            crossAxisCount = 3;
            childAspectRatio = 0.7;
            spacing = 16.0;
          } else if (screenWidth > 375) {
            crossAxisCount = 2;
            childAspectRatio = 0.65;
            spacing = 12.0;
          } else {
            // Very small screens (iPhone SE)
            crossAxisCount = 1;
            childAspectRatio = 1.2;
            spacing = 16.0;
          }

          return GridView.builder(
            padding: EdgeInsets.fromLTRB(
              screenSize.width * 0.04, // 4% horizontal padding
              0,
              screenSize.width * 0.04,
              16.0,
            ),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: spacing,
              mainAxisSpacing: spacing,
              childAspectRatio: childAspectRatio,
            ),
            itemCount: stories.length,
            itemBuilder: (context, index) {
              final story = stories[index];
              return StoryCoverCardWidget(
                story: story,
                languageCode: languageCode,
                onTap: () => _onStoryTap(context, story),
                isOneColumn: crossAxisCount == 1,
              );
            },
          );
        },
      ),
    );
  }
}
