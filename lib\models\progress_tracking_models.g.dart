// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'progress_tracking_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ReadingProgress _$ReadingProgressFromJson(Map<String, dynamic> json) =>
    ReadingProgress(
      totalReadingTime: (json['totalReadingTime'] as num).toInt(),
      storiesCompleted: (json['storiesCompleted'] as num).toInt(),
      totalSessions: (json['totalSessions'] as num).toInt(),
      averageSessionTime: (json['averageSessionTime'] as num).toDouble(),
      lastReadingDate: _dateTimeFromJson(json['lastReadingDate'] as String),
      favoriteGenre: json['favoriteGenre'] as String,
      weeklyProgress: Map<String, int>.from(json['weeklyProgress'] as Map),
      monthlyProgress: Map<String, int>.from(json['monthlyProgress'] as Map),
    );

Map<String, dynamic> _$ReadingProgressToJson(ReadingProgress instance) =>
    <String, dynamic>{
      'totalReadingTime': instance.totalReadingTime,
      'storiesCompleted': instance.storiesCompleted,
      'totalSessions': instance.totalSessions,
      'averageSessionTime': instance.averageSessionTime,
      'lastReadingDate': _dateTimeToJson(instance.lastReadingDate),
      'favoriteGenre': instance.favoriteGenre,
      'weeklyProgress': instance.weeklyProgress,
      'monthlyProgress': instance.monthlyProgress,
    };

ReadingSession _$ReadingSessionFromJson(Map<String, dynamic> json) =>
    ReadingSession(
      id: json['id'] as String,
      storyId: json['storyId'] as String,
      storyTitle: json['storyTitle'] as String,
      startTime: _dateTimeFromJson(json['startTime'] as String),
      endTime: _dateTimeFromJsonNullable(json['endTime'] as String?),
      durationMinutes: (json['durationMinutes'] as num).toInt(),
      completed: json['completed'] as bool,
    );

Map<String, dynamic> _$ReadingSessionToJson(ReadingSession instance) =>
    <String, dynamic>{
      'id': instance.id,
      'storyId': instance.storyId,
      'storyTitle': instance.storyTitle,
      'startTime': _dateTimeToJson(instance.startTime),
      'endTime': _dateTimeToJsonNullable(instance.endTime),
      'durationMinutes': instance.durationMinutes,
      'completed': instance.completed,
    };

ReadingStreak _$ReadingStreakFromJson(Map<String, dynamic> json) =>
    ReadingStreak(
      currentStreak: (json['currentStreak'] as num).toInt(),
      longestStreak: (json['longestStreak'] as num).toInt(),
      lastReadingDate: _dateTimeFromJson(json['lastReadingDate'] as String),
      streakDates: _dateTimeListFromJson(json['streakDates'] as List),
    );

Map<String, dynamic> _$ReadingStreakToJson(ReadingStreak instance) =>
    <String, dynamic>{
      'currentStreak': instance.currentStreak,
      'longestStreak': instance.longestStreak,
      'lastReadingDate': _dateTimeToJson(instance.lastReadingDate),
      'streakDates': _dateTimeListToJson(instance.streakDates),
    };

Achievement _$AchievementFromJson(Map<String, dynamic> json) => Achievement(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      icon: json['icon'] as String,
      unlockedAt: _dateTimeFromJson(json['unlockedAt'] as String),
    );

Map<String, dynamic> _$AchievementToJson(Achievement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'icon': instance.icon,
      'unlockedAt': _dateTimeToJson(instance.unlockedAt),
    };
