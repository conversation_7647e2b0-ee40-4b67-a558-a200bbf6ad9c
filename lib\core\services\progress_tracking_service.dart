import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Service for tracking reading progress with local storage and optional Firebase sync
class ProgressTrackingService {
  static const String _logPrefix = '[ProgressTrackingService]';
  static const String _progressKey = 'reading_progress';
  static const String _sessionKey = 'reading_sessions';
  static const String _streakKey = 'reading_streak';
  static const String _achievementsKey = 'achievements';

  static ProgressTrackingService? _instance;
  static ProgressTrackingService get instance {
    _instance ??= ProgressTrackingService._();
    return _instance!;
  }

  ProgressTrackingService._();

  // Local data
  ReadingProgress? _currentProgress;
  List<ReadingSession> _sessions = [];
  ReadingStreak? _streak;
  List<Achievement> _achievements = [];

  // Stream controllers
  final StreamController<ReadingProgress> _progressController = StreamController<ReadingProgress>.broadcast();
  final StreamController<List<ReadingSession>> _sessionsController = StreamController<List<ReadingSession>>.broadcast();
  final StreamController<ReadingStreak> _streakController = StreamController<ReadingStreak>.broadcast();
  final StreamController<List<Achievement>> _achievementsController = StreamController<List<Achievement>>.broadcast();

  // Getters for streams
  Stream<ReadingProgress> get progressStream => _progressController.stream;
  Stream<List<ReadingSession>> get sessionsStream => _sessionsController.stream;
  Stream<ReadingStreak> get streakStream => _streakController.stream;
  Stream<List<Achievement>> get achievementsStream => _achievementsController.stream;

  // Getters for current state
  ReadingProgress? get currentProgress => _currentProgress;
  List<ReadingSession> get sessions => List.unmodifiable(_sessions);
  ReadingStreak? get streak => _streak;
  List<Achievement> get achievements => List.unmodifiable(_achievements);

  /// Initialize the service and load data
  Future<void> initialize() async {
    try {
      AppLogger.info('$_logPrefix: Initializing progress tracking service');
      await _loadLocalData();
      await _initializeStreak();
      AppLogger.info('$_logPrefix: Progress tracking service initialized');
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error initializing service', e, stackTrace);
    }
  }

  /// Start a new reading session
  Future<void> startSession(String storyId, String storyTitle) async {
    try {
      final session = ReadingSession(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        storyId: storyId,
        storyTitle: storyTitle,
        startTime: DateTime.now(),
        endTime: null,
        durationMinutes: 0,
        completed: false,
      );

      _sessions.add(session);
      await _saveLocalData();
      _sessionsController.add(List.from(_sessions));

      AppLogger.info('$_logPrefix: Started reading session for story: $storyId');
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error starting session', e, stackTrace);
    }
  }

  /// End the current reading session
  Future<void> endSession(String sessionId, {bool completed = false}) async {
    try {
      final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
      if (sessionIndex == -1) return;

      final session = _sessions[sessionIndex];
      final endTime = DateTime.now();
      final duration = endTime.difference(session.startTime).inMinutes;

      final updatedSession = session.copyWith(
        endTime: endTime,
        durationMinutes: duration,
        completed: completed,
      );

      _sessions[sessionIndex] = updatedSession;
      await _updateProgress(updatedSession);
      await _updateStreak(completed);
      await _checkAchievements();
      await _saveLocalData();

      _sessionsController.add(List.from(_sessions));

      AppLogger.info('$_logPrefix: Ended reading session: $sessionId (${duration}min, completed: $completed)');
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error ending session', e, stackTrace);
    }
  }

  /// Update overall reading progress
  Future<void> _updateProgress(ReadingSession session) async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      _currentProgress ??= ReadingProgress(
        totalReadingTime: 0,
        storiesCompleted: 0,
        totalSessions: 0,
        averageSessionTime: 0.0,
        lastReadingDate: today,
        favoriteGenre: 'Adventure',
        weeklyProgress: {},
        monthlyProgress: {},
      );

      // Update totals
      _currentProgress = _currentProgress!.copyWith(
        totalReadingTime: _currentProgress!.totalReadingTime + session.durationMinutes,
        storiesCompleted: session.completed 
            ? _currentProgress!.storiesCompleted + 1 
            : _currentProgress!.storiesCompleted,
        totalSessions: _currentProgress!.totalSessions + 1,
        lastReadingDate: today,
      );

      // Calculate average session time
      final totalMinutes = _sessions.fold(0, (sum, s) => sum + s.durationMinutes);
      final completedSessions = _sessions.where((s) => s.endTime != null).length;
      _currentProgress = _currentProgress!.copyWith(
        averageSessionTime: completedSessions > 0 ? totalMinutes / completedSessions : 0.0,
      );

      // Update weekly progress
      final weekKey = _getWeekKey(today);
      final weeklyProgress = Map<String, int>.from(_currentProgress!.weeklyProgress);
      weeklyProgress[weekKey] = (weeklyProgress[weekKey] ?? 0) + session.durationMinutes;
      _currentProgress = _currentProgress!.copyWith(weeklyProgress: weeklyProgress);

      // Update monthly progress
      final monthKey = _getMonthKey(today);
      final monthlyProgress = Map<String, int>.from(_currentProgress!.monthlyProgress);
      monthlyProgress[monthKey] = (monthlyProgress[monthKey] ?? 0) + session.durationMinutes;
      _currentProgress = _currentProgress!.copyWith(monthlyProgress: monthlyProgress);

      _progressController.add(_currentProgress!);
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error updating progress', e, stackTrace);
    }
  }

  /// Update reading streak
  Future<void> _updateStreak(bool completed) async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      _streak ??= ReadingStreak(
        currentStreak: 0,
        longestStreak: 0,
        lastReadingDate: today,
        streakDates: [],
      );

      if (completed) {
        final lastDate = _streak!.lastReadingDate;
        final daysDifference = today.difference(lastDate).inDays;

        if (daysDifference == 0) {
          // Same day, no change to streak
          return;
        } else if (daysDifference == 1) {
          // Consecutive day, increment streak
          _streak = _streak!.copyWith(
            currentStreak: _streak!.currentStreak + 1,
            lastReadingDate: today,
            streakDates: [..._streak!.streakDates, today],
          );
        } else {
          // Streak broken, start new streak
          _streak = _streak!.copyWith(
            currentStreak: 1,
            lastReadingDate: today,
            streakDates: [today],
          );
        }

        // Update longest streak
        if (_streak!.currentStreak > _streak!.longestStreak) {
          _streak = _streak!.copyWith(longestStreak: _streak!.currentStreak);
        }

        _streakController.add(_streak!);
      }
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error updating streak', e, stackTrace);
    }
  }

  /// Check and award achievements
  Future<void> _checkAchievements() async {
    try {
      final newAchievements = <Achievement>[];

      // First story achievement
      if (_currentProgress!.storiesCompleted >= 1 && !_hasAchievement('first_story')) {
        newAchievements.add(Achievement(
          id: 'first_story',
          title: 'First Story',
          description: 'Completed your first story!',
          icon: 'auto_stories',
          unlockedAt: DateTime.now(),
        ));
      }

      // Reading streak achievements
      if (_streak!.currentStreak >= 3 && !_hasAchievement('streak_3')) {
        newAchievements.add(Achievement(
          id: 'streak_3',
          title: '3-Day Streak',
          description: 'Read stories for 3 days in a row!',
          icon: 'local_fire_department',
          unlockedAt: DateTime.now(),
        ));
      }

      // Time-based achievements
      if (_currentProgress!.totalReadingTime >= 60 && !_hasAchievement('hour_reader')) {
        newAchievements.add(Achievement(
          id: 'hour_reader',
          title: 'Hour Reader',
          description: 'Read for a total of 1 hour!',
          icon: 'access_time',
          unlockedAt: DateTime.now(),
        ));
      }

      if (newAchievements.isNotEmpty) {
        _achievements.addAll(newAchievements);
        _achievementsController.add(List.from(_achievements));
        AppLogger.info('$_logPrefix: Unlocked ${newAchievements.length} new achievements');
      }
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error checking achievements', e, stackTrace);
    }
  }

  /// Check if user has specific achievement
  bool _hasAchievement(String achievementId) {
    return _achievements.any((a) => a.id == achievementId);
  }

  /// Load data from local storage
  Future<void> _loadLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load progress
      final progressJson = prefs.getString(_progressKey);
      if (progressJson != null) {
        _currentProgress = ReadingProgress.fromJson(jsonDecode(progressJson));
      }

      // Load sessions
      final sessionsJson = prefs.getString(_sessionKey);
      if (sessionsJson != null) {
        final sessionsList = jsonDecode(sessionsJson) as List;
        _sessions = sessionsList.map((json) => ReadingSession.fromJson(json)).toList();
      }

      // Load streak
      final streakJson = prefs.getString(_streakKey);
      if (streakJson != null) {
        _streak = ReadingStreak.fromJson(jsonDecode(streakJson));
      }

      // Load achievements
      final achievementsJson = prefs.getString(_achievementsKey);
      if (achievementsJson != null) {
        final achievementsList = jsonDecode(achievementsJson) as List;
        _achievements = achievementsList.map((json) => Achievement.fromJson(json)).toList();
      }

      AppLogger.info('$_logPrefix: Local data loaded successfully');
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error loading local data', e, stackTrace);
    }
  }

  /// Save data to local storage
  Future<void> _saveLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (_currentProgress != null) {
        await prefs.setString(_progressKey, jsonEncode(_currentProgress!.toJson()));
      }

      await prefs.setString(_sessionKey, jsonEncode(_sessions.map((s) => s.toJson()).toList()));

      if (_streak != null) {
        await prefs.setString(_streakKey, jsonEncode(_streak!.toJson()));
      }

      await prefs.setString(_achievementsKey, jsonEncode(_achievements.map((a) => a.toJson()).toList()));

      AppLogger.debug('$_logPrefix: Local data saved successfully');
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error saving local data', e, stackTrace);
    }
  }

  /// Initialize streak if not exists
  Future<void> _initializeStreak() async {
    if (_streak == null) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      _streak = ReadingStreak(
        currentStreak: 0,
        longestStreak: 0,
        lastReadingDate: today,
        streakDates: [],
      );
    }
  }

  /// Get week key for progress tracking
  String _getWeekKey(DateTime date) {
    final startOfWeek = date.subtract(Duration(days: date.weekday - 1));
    return '${startOfWeek.year}-W${startOfWeek.month.toString().padLeft(2, '0')}-${startOfWeek.day.toString().padLeft(2, '0')}';
  }

  /// Get month key for progress tracking
  String _getMonthKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}';
  }

  /// Sync with Firebase (optional)
  Future<void> syncWithFirebase() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        AppLogger.info('$_logPrefix: No authenticated user, skipping Firebase sync');
        return;
      }

      final firestore = FirebaseFirestore.instance;
      final userDoc = firestore.collection('users').doc(user.uid);

      // Upload progress data
      if (_currentProgress != null) {
        await userDoc.collection('progress').doc('current').set(_currentProgress!.toJson());
      }

      // Upload recent sessions (last 30 days)
      final recentSessions = _sessions.where((s) => 
        s.startTime.isAfter(DateTime.now().subtract(const Duration(days: 30)))
      ).toList();

      for (final session in recentSessions) {
        await userDoc.collection('sessions').doc(session.id).set(session.toJson());
      }

      AppLogger.info('$_logPrefix: Successfully synced with Firebase');
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error syncing with Firebase', e, stackTrace);
    }
  }

  /// Dispose resources
  void dispose() {
    _progressController.close();
    _sessionsController.close();
    _streakController.close();
    _achievementsController.close();
  }
}
