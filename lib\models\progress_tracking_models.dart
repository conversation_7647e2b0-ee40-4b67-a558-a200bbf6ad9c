import 'package:json_annotation/json_annotation.dart';

part 'progress_tracking_models.g.dart';

/// Model for overall reading progress
@JsonSerializable()
class ReadingProgress {
  /// Total reading time in minutes
  final int totalReadingTime;

  /// Number of stories completed
  final int storiesCompleted;

  /// Total number of reading sessions
  final int totalSessions;

  /// Average session time in minutes
  final double averageSessionTime;

  /// Last reading date
  @JsonKey(fromJson: _dateTimeFromJson, toJson: _dateTimeToJson)
  final DateTime lastReadingDate;

  /// Favorite genre based on reading history
  final String favoriteGenre;

  /// Weekly progress (week key -> minutes read)
  final Map<String, int> weeklyProgress;

  /// Monthly progress (month key -> minutes read)
  final Map<String, int> monthlyProgress;

  const ReadingProgress({
    required this.totalReadingTime,
    required this.storiesCompleted,
    required this.totalSessions,
    required this.averageSessionTime,
    required this.lastReadingDate,
    required this.favoriteGenre,
    required this.weeklyProgress,
    required this.monthlyProgress,
  });

  factory ReadingProgress.fromJson(Map<String, dynamic> json) =>
      _$ReadingProgressFromJson(json);

  Map<String, dynamic> toJson() => _$ReadingProgressToJson(this);

  ReadingProgress copyWith({
    int? totalReadingTime,
    int? storiesCompleted,
    int? totalSessions,
    double? averageSessionTime,
    DateTime? lastReadingDate,
    String? favoriteGenre,
    Map<String, int>? weeklyProgress,
    Map<String, int>? monthlyProgress,
  }) {
    return ReadingProgress(
      totalReadingTime: totalReadingTime ?? this.totalReadingTime,
      storiesCompleted: storiesCompleted ?? this.storiesCompleted,
      totalSessions: totalSessions ?? this.totalSessions,
      averageSessionTime: averageSessionTime ?? this.averageSessionTime,
      lastReadingDate: lastReadingDate ?? this.lastReadingDate,
      favoriteGenre: favoriteGenre ?? this.favoriteGenre,
      weeklyProgress: weeklyProgress ?? this.weeklyProgress,
      monthlyProgress: monthlyProgress ?? this.monthlyProgress,
    );
  }
}

/// Model for individual reading sessions
@JsonSerializable()
class ReadingSession {
  /// Unique session ID
  final String id;

  /// Story ID being read
  final String storyId;

  /// Story title
  final String storyTitle;

  /// Session start time
  @JsonKey(fromJson: _dateTimeFromJson, toJson: _dateTimeToJson)
  final DateTime startTime;

  /// Session end time (null if ongoing)
  @JsonKey(fromJson: _dateTimeFromJsonNullable, toJson: _dateTimeToJsonNullable)
  final DateTime? endTime;

  /// Duration in minutes
  final int durationMinutes;

  /// Whether the story was completed in this session
  final bool completed;

  const ReadingSession({
    required this.id,
    required this.storyId,
    required this.storyTitle,
    required this.startTime,
    this.endTime,
    required this.durationMinutes,
    required this.completed,
  });

  factory ReadingSession.fromJson(Map<String, dynamic> json) =>
      _$ReadingSessionFromJson(json);

  Map<String, dynamic> toJson() => _$ReadingSessionToJson(this);

  ReadingSession copyWith({
    String? id,
    String? storyId,
    String? storyTitle,
    DateTime? startTime,
    DateTime? endTime,
    int? durationMinutes,
    bool? completed,
  }) {
    return ReadingSession(
      id: id ?? this.id,
      storyId: storyId ?? this.storyId,
      storyTitle: storyTitle ?? this.storyTitle,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      completed: completed ?? this.completed,
    );
  }
}

/// Model for reading streak tracking
@JsonSerializable()
class ReadingStreak {
  /// Current consecutive reading streak in days
  final int currentStreak;

  /// Longest streak achieved
  final int longestStreak;

  /// Last reading date
  @JsonKey(fromJson: _dateTimeFromJson, toJson: _dateTimeToJson)
  final DateTime lastReadingDate;

  /// List of dates in current streak
  @JsonKey(fromJson: _dateTimeListFromJson, toJson: _dateTimeListToJson)
  final List<DateTime> streakDates;

  const ReadingStreak({
    required this.currentStreak,
    required this.longestStreak,
    required this.lastReadingDate,
    required this.streakDates,
  });

  factory ReadingStreak.fromJson(Map<String, dynamic> json) =>
      _$ReadingStreakFromJson(json);

  Map<String, dynamic> toJson() => _$ReadingStreakToJson(this);

  ReadingStreak copyWith({
    int? currentStreak,
    int? longestStreak,
    DateTime? lastReadingDate,
    List<DateTime>? streakDates,
  }) {
    return ReadingStreak(
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      lastReadingDate: lastReadingDate ?? this.lastReadingDate,
      streakDates: streakDates ?? this.streakDates,
    );
  }
}

/// Model for achievements
@JsonSerializable()
class Achievement {
  /// Unique achievement ID
  final String id;

  /// Achievement title
  final String title;

  /// Achievement description
  final String description;

  /// Icon name/identifier
  final String icon;

  /// When the achievement was unlocked
  @JsonKey(fromJson: _dateTimeFromJson, toJson: _dateTimeToJson)
  final DateTime unlockedAt;

  const Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.unlockedAt,
  });

  factory Achievement.fromJson(Map<String, dynamic> json) =>
      _$AchievementFromJson(json);

  Map<String, dynamic> toJson() => _$AchievementToJson(this);
}

/// Model for daily progress data (for charts)
class DailyProgressData {
  final DateTime date;
  final int minutesRead;
  final int storiesCompleted;
  final int sessionsCount;

  const DailyProgressData({
    required this.date,
    required this.minutesRead,
    required this.storiesCompleted,
    required this.sessionsCount,
  });
}

/// Model for weekly summary
class WeeklySummary {
  final DateTime weekStart;
  final DateTime weekEnd;
  final int totalMinutes;
  final int storiesCompleted;
  final int sessionsCount;
  final List<DailyProgressData> dailyData;

  const WeeklySummary({
    required this.weekStart,
    required this.weekEnd,
    required this.totalMinutes,
    required this.storiesCompleted,
    required this.sessionsCount,
    required this.dailyData,
  });
}

/// Model for monthly summary
class MonthlySummary {
  final DateTime monthStart;
  final DateTime monthEnd;
  final int totalMinutes;
  final int storiesCompleted;
  final int sessionsCount;
  final List<WeeklySummary> weeklyData;

  const MonthlySummary({
    required this.monthStart,
    required this.monthEnd,
    required this.totalMinutes,
    required this.storiesCompleted,
    required this.sessionsCount,
    required this.weeklyData,
  });
}

// Helper functions for DateTime JSON serialization
DateTime _dateTimeFromJson(String json) => DateTime.parse(json);
String _dateTimeToJson(DateTime dateTime) => dateTime.toIso8601String();

DateTime? _dateTimeFromJsonNullable(String? json) => 
    json != null ? DateTime.parse(json) : null;
String? _dateTimeToJsonNullable(DateTime? dateTime) => 
    dateTime?.toIso8601String();

List<DateTime> _dateTimeListFromJson(List<dynamic> json) =>
    json.map((e) => DateTime.parse(e as String)).toList();
List<String> _dateTimeListToJson(List<DateTime> dates) =>
    dates.map((date) => date.toIso8601String()).toList();
