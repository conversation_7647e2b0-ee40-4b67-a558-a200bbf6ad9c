import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/providers/progress_tracking_provider.dart';
import 'package:choice_once_upon_a_time/models/progress_tracking_models.dart';

/// Screen for tracking reading progress and analytics
class ProgressTrackingScreen extends ConsumerStatefulWidget {
  const ProgressTrackingScreen({super.key});

  @override
  ConsumerState<ProgressTrackingScreen> createState() => _ProgressTrackingScreenState();
}

class _ProgressTrackingScreenState extends ConsumerState<ProgressTrackingScreen> {
  String _selectedPeriod = 'week';
  String _selectedChild = 'all';

  @override
  void initState() {
    super.initState();
    // Initialize the progress tracking service
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(progressTrackingServiceProvider).initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    final progressAsync = ref.watch(currentProgressProvider);
    final streakAsync = ref.watch(readingStreakProvider);
    final achievementsAsync = ref.watch(achievementsProvider);
    final dailyProgressData = ref.watch(dailyProgressProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Progress Tracking'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: progressAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.grey),
              const SizedBox(height: 16),
              Text('Error loading progress data', style: theme.textTheme.titleMedium),
              const SizedBox(height: 8),
              Text(error.toString(), style: theme.textTheme.bodySmall),
            ],
          ),
        ),
        data: (progress) => SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with filters
              _buildHeader(theme, isSmallScreen),
              const SizedBox(height: 24),

              if (progress != null) ...[
                // Summary cards
                _buildSummaryCards(theme, progress, isSmallScreen),
                const SizedBox(height: 24),

                // Reading streak
                streakAsync.when(
                  data: (streak) => _buildStreakCard(theme, streak, isSmallScreen),
                  loading: () => const SizedBox.shrink(),
                  error: (_, __) => const SizedBox.shrink(),
                ),
                const SizedBox(height: 24),

                // Daily progress chart
                if (_selectedPeriod == 'week') ...[
                  _buildDailyProgressChart(theme, dailyProgressData, isSmallScreen),
                  const SizedBox(height: 24),
                ],

                // Achievements
                achievementsAsync.when(
                  data: (achievements) => _buildAchievements(theme, achievements, isSmallScreen),
                  loading: () => const SizedBox.shrink(),
                  error: (_, __) => const SizedBox.shrink(),
                ),
                const SizedBox(height: 24),

                // Recommendations
                _buildRecommendations(theme, isSmallScreen),
              ] else ...[
                // No data state
                _buildNoDataState(theme, isSmallScreen),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isSmallScreen) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: theme.colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Reading Analytics',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Filters
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedPeriod,
                  decoration: const InputDecoration(
                    labelText: 'Time Period',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'week', child: Text('This Week')),
                    DropdownMenuItem(value: 'month', child: Text('This Month')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedPeriod = value;
                      });
                      AppLogger.debug('ProgressTracking: Changed period to $value');
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedChild,
                  decoration: const InputDecoration(
                    labelText: 'Child',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('All Children')),
                    DropdownMenuItem(value: 'emma', child: Text('Emma')),
                    DropdownMenuItem(value: 'alex', child: Text('Alex')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedChild = value;
                      });
                      AppLogger.debug('ProgressTracking: Changed child filter to $value');
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(ThemeData theme, ReadingProgress data, bool isSmallScreen) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: isSmallScreen ? 2 : 4,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: isSmallScreen ? 1.2 : 1.0,
      children: [
        _buildSummaryCard(
          theme,
          'Reading Time',
          '${data.totalReadingTime}min',
          Icons.access_time,
          Colors.blue,
        ),
        _buildSummaryCard(
          theme,
          'Stories Completed',
          '${data.storiesCompleted}',
          Icons.auto_stories,
          Colors.green,
        ),
        _buildSummaryCard(
          theme,
          'Avg Session',
          '${data.averageSessionTime.toStringAsFixed(1)}min',
          Icons.timer,
          Colors.orange,
        ),
        _buildSummaryCard(
          theme,
          'Favorite Genre',
          data.favoriteGenre,
          Icons.favorite,
          Colors.pink,
        ),
      ],
    );
  }

  Widget _buildSummaryCard(ThemeData theme, String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStreakCard(ThemeData theme, ReadingStreak? streak, bool isSmallScreen) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.local_fire_department,
                color: Colors.orange,
                size: isSmallScreen ? 32 : 40,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Reading Streak',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${streak?.currentStreak ?? 0} days in a row!',
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Keep up the great reading habit!',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyProgressChart(ThemeData theme, List<DailyProgressData> dailyData, bool isSmallScreen) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Daily Reading Progress',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 120,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: dailyData.map((day) {
                  final maxMinutes = dailyData.isNotEmpty
                      ? dailyData.map((d) => d.minutesRead).reduce((a, b) => a > b ? a : b)
                      : 1;
                  final height = maxMinutes > 0 ? (day.minutesRead / maxMinutes) * 80 : 0.0;
                  
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        width: 24,
                        height: height,
                        decoration: BoxDecoration(
                          color: day.minutesRead > 0
                              ? theme.colorScheme.primary
                              : Colors.grey[300],
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _getDayName(day.date),
                        style: theme.textTheme.bodySmall,
                      ),
                      Text(
                        '${day.minutesRead}m',
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontSize: 10,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievements(ThemeData theme, List<Achievement> achievements, bool isSmallScreen) {
    final achievements = [
      Achievement('First Story', 'Completed your first story!', Icons.star, true),
      Achievement('Speed Reader', 'Read 5 stories in one week', Icons.flash_on, true),
      Achievement('Bookworm', 'Read for 7 days straight', Icons.menu_book, false),
      Achievement('Explorer', 'Try 3 different story genres', Icons.explore, false),
    ];

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Achievements',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...achievements.map((achievement) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: achievement.isUnlocked
                          ? Colors.amber.withValues(alpha: 0.2)
                          : Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      achievement.icon,
                      color: achievement.isUnlocked ? Colors.amber : Colors.grey,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          achievement.title,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: achievement.isUnlocked 
                                ? null 
                                : theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                        Text(
                          achievement.description,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (achievement.isUnlocked)
                    const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 20,
                    ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendations(ThemeData theme, bool isSmallScreen) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recommendations',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildRecommendationItem(
              theme,
              'Try reading at bedtime',
              'Studies show bedtime reading improves sleep quality.',
              Icons.bedtime,
              Colors.purple,
            ),
            const SizedBox(height: 12),
            _buildRecommendationItem(
              theme,
              'Explore new genres',
              'Variety in reading helps develop different skills.',
              Icons.explore,
              Colors.teal,
            ),
            const SizedBox(height: 12),
            _buildRecommendationItem(
              theme,
              'Set a daily goal',
              'Aim for 20 minutes of reading each day.',
              Icons.flag,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationItem(ThemeData theme, String title, String description, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// Data models
class ProgressData {
  final int totalReadingTime;
  final int storiesCompleted;
  final double averageSessionTime;
  final int streakDays;
  final String favoriteGenre;
  final List<DailyProgress> dailyProgress;

  ProgressData({
    required this.totalReadingTime,
    required this.storiesCompleted,
    required this.averageSessionTime,
    required this.streakDays,
    required this.favoriteGenre,
    required this.dailyProgress,
  });
}

class DailyProgress {
  final String day;
  final int minutes;
  final int stories;

  DailyProgress({
    required this.day,
    required this.minutes,
    required this.stories,
  });
}

  /// Get day name from date
  String _getDayName(DateTime date) {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days[date.weekday - 1];
  }

  /// Build no data state
  Widget _buildNoDataState(ThemeData theme, bool isSmallScreen) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 64,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No Reading Data Yet',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start reading stories to see your progress here!',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

class Achievement {
  final String title;
  final String description;
  final IconData icon;
  final bool isUnlocked;

  Achievement(this.title, this.description, this.icon, this.isUnlocked);
}
