import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/enhanced_narration_widget.dart';

/// Enhanced landscape story widget with full-screen images and two-row controls
class EnhancedLandscapeStoryWidget extends StatefulWidget {
  final EnhancedStoryModel story;
  final EnhancedSceneModel scene;
  final Function(ChoiceOptionModel) onChoiceSelected;
  final VoidCallback? onSceneComplete;
  final StoryNarrationService narrationService;
  final StorySettingsService settingsService;

  const EnhancedLandscapeStoryWidget({
    super.key,
    required this.story,
    required this.scene,
    required this.onChoiceSelected,
    required this.narrationService,
    required this.settingsService,
    this.onSceneComplete,
  });

  @override
  State<EnhancedLandscapeStoryWidget> createState() => _EnhancedLandscapeStoryWidgetState();
}

class _EnhancedLandscapeStoryWidgetState extends State<EnhancedLandscapeStoryWidget>
    with TickerProviderStateMixin {
  
  late AnimationController _fadeController;
  late AnimationController _progressController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _progressAnimation;

  // Image caching for stability
  ImageProvider? _cachedImageProvider;
  
  // Narration state
  bool _isNarrating = false;
  bool _isPaused = false;
  bool _hasCompleted = false;
  int _currentSentenceIndex = 0;
  List<String> _sentences = [];
  
  // UI state
  bool _showChoices = false;
  bool _showOverlayControls = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isInitialized) {
      _isInitialized = true;
      _initializeScene();
    }
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));
  }

  void _initializeScene() {
    AppLogger.debug('[SCENE_DEBUG] Scene ${widget.scene.id} initialized - Image: ${widget.scene.getImagePath(widget.story.storyId)}');

    // Cache the image for stability
    _cacheImage();

    // Split text into sentences for progress tracking
    _sentences = _splitIntoSentences(widget.scene.text);

    // Listen to narration service progress
    widget.narrationService.progressStream.listen((progress) {
      if (mounted) {
        setState(() {
          _currentSentenceIndex = progress.currentSentence - 1; // Convert to 0-based index
        });
      }
    });

    // Listen to narration state changes
    widget.narrationService.narrationStateStream.listen((isNarrating) {
      if (mounted) {
        setState(() {
          _isNarrating = isNarrating;
          if (!isNarrating) {
            _hasCompleted = true;
          }
        });
      }
    });

    // Start fade-in animation
    _fadeController.forward();

    // Auto-start narration after a brief delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _startNarration();
      }
    });
  }

  void _cacheImage() {
    final imagePath = widget.scene.getImagePath(widget.story.storyId);
    _cachedImageProvider = AssetImage(imagePath);
    
    // Pre-load the image
    precacheImage(_cachedImageProvider!, context).catchError((error) {
      AppLogger.error('[SCENE_DEBUG] Failed to cache image: $imagePath', error);
    });
  }

  List<String> _splitIntoSentences(String text) {
    // Split by sentence-ending punctuation
    final sentences = text.split(RegExp(r'[.!?]+'))
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();
    
    return sentences.isNotEmpty ? sentences : [text];
  }

  Future<void> _startNarration() async {
    if (_isNarrating || _hasCompleted) return;

    setState(() {
      _isNarrating = true;
      _isPaused = false;
      _currentSentenceIndex = 0;
    });

    AppLogger.debug('[SCENE_DEBUG] Narrating: "${widget.scene.text.substring(0, widget.scene.text.length > 50 ? 50 : widget.scene.text.length)}${widget.scene.text.length > 50 ? '...' : ''}"');

    try {
      // Start narration with sentence-level progress tracking
      await _narrateWithProgress();
      
      await _completeNarration();
    } catch (e) {
      AppLogger.error('[SCENE_DEBUG] Narration error', e);
      await _stopNarration();
    }
  }

  Future<void> _narrateWithProgress() async {
    // Use the scene narration service which handles sentence-level progress internally
    await widget.narrationService.narrateScene(widget.scene);
  }

  Future<void> _pauseNarration() async {
    if (_isNarrating && !_isPaused) {
      setState(() {
        _isPaused = true;
      });
      await widget.narrationService.pause();
      AppLogger.debug('[SCENE_DEBUG] Narration paused');
    }
  }

  Future<void> _resumeNarration() async {
    if (_isNarrating && _isPaused) {
      setState(() {
        _isPaused = false;
      });
      await widget.narrationService.resume();
      AppLogger.debug('[SCENE_DEBUG] Narration resumed');
    }
  }

  Future<void> _stopNarration() async {
    setState(() {
      _isNarrating = false;
      _isPaused = false;
    });
    await widget.narrationService.stop();
    AppLogger.debug('[SCENE_DEBUG] Narration stopped');
  }

  Future<void> _completeNarration() async {
    AppLogger.debug('[SCENE_DEBUG] Narration completed successfully');
    
    setState(() {
      _isNarrating = false;
      _hasCompleted = true;
      _currentSentenceIndex = _sentences.length - 1;
    });

    await _progressController.forward();

    // Show choices or next scene button
    if (widget.scene.choices?.isNotEmpty ?? false) {
      setState(() {
        _showChoices = true;
      });
    } else {
      // Auto-advance if enabled in settings
      final autoPlay = widget.settingsService.autoPlay;
      if (autoPlay) {
        await Future.delayed(const Duration(seconds: 2));
        if (mounted) {
          widget.onSceneComplete?.call();
        }
      }
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Full-screen background image
          _buildFullScreenImage(),
          
          // Overlay controls (top-right)
          _buildOverlayControls(),
          
          // Two-row control interface (bottom)
          _buildControlInterface(),
          
          // Choice selection overlay
          if (_showChoices) _buildChoiceOverlay(),
        ],
      ),
    );
  }

  Widget _buildFullScreenImage() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          image: _cachedImageProvider != null
              ? DecorationImage(
                  image: _cachedImageProvider!,
                  fit: BoxFit.cover,
                )
              : null,
        ),
        child: _cachedImageProvider == null
            ? Container(
                color: Colors.grey[300],
                child: const Center(
                  child: Icon(Icons.image, size: 64, color: Colors.grey),
                ),
              )
            : null,
      ),
    );
  }

  Widget _buildOverlayControls() {
    return Positioned(
      top: 16,
      right: 16,
      child: SafeArea(
        child: Column(
          children: [
            // Settings button
            IconButton(
              onPressed: () {
                setState(() {
                  _showOverlayControls = !_showOverlayControls;
                });
              },
              icon: const Icon(Icons.settings),
              style: IconButton.styleFrom(
                backgroundColor: Colors.black54,
                foregroundColor: Colors.white,
              ),
            ),
            
            // Overlay controls panel
            if (_showOverlayControls) _buildOverlayPanel(),
          ],
        ),
      ),
    );
  }

  Widget _buildOverlayPanel() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Font size slider
          _buildSliderControl(
            'Font Size',
            widget.settingsService.subtitleSize,
            12.0,
            32.0,
            widget.settingsService.setSubtitleSize,
          ),
          
          const SizedBox(height: 12),
          
          // Narration speed slider
          _buildSliderControl(
            'Speed',
            widget.settingsService.narrationSpeed * 100,
            10.0,
            100.0,
            (value) => widget.settingsService.setNarrationSpeed(value / 100),
          ),
        ],
      ),
    );
  }

  Widget _buildSliderControl(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ${value.round()}',
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
        SizedBox(
          width: 150,
          child: Slider(
            value: value.clamp(min, max),
            min: min,
            max: max,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Widget _buildControlInterface() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: SafeArea(
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [
                Colors.black.withValues(alpha: 0.8),
                Colors.transparent,
              ],
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Row 1: Main Controls
              _buildMainControlsRow(),

              const SizedBox(height: 12),

              // Row 2: Progress Indicators
              _buildProgressRow(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainControlsRow() {
    return Row(
      children: [
        // Left Section: Play/Pause Button
        Expanded(
          flex: 1,
          child: _buildPlayPauseButton(),
        ),

        // Middle Section: Current Text Display
        Expanded(
          flex: 3,
          child: _buildCurrentTextDisplay(),
        ),

        // Right Section: Next/Choice Button
        Expanded(
          flex: 1,
          child: _buildNextButton(),
        ),
      ],
    );
  }

  Widget _buildPlayPauseButton() {
    return Center(
      child: IconButton(
        onPressed: _isNarrating
            ? (_isPaused ? _resumeNarration : _pauseNarration)
            : _startNarration,
        icon: Icon(
          _isNarrating
              ? (_isPaused ? Icons.play_arrow : Icons.pause)
              : Icons.play_arrow,
          size: 32,
        ),
        style: IconButton.styleFrom(
          backgroundColor: Colors.white.withValues(alpha: 0.9),
          foregroundColor: Colors.blue[800],
          padding: const EdgeInsets.all(12),
        ),
      ),
    );
  }

  Widget _buildCurrentTextDisplay() {
    final currentText = _currentSentenceIndex < _sentences.length
        ? _sentences[_currentSentenceIndex]
        : widget.scene.text;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(8),
      ),
      child: StreamBuilder<double>(
        stream: widget.settingsService.subtitleSizeStream,
        initialData: widget.settingsService.subtitleSize,
        builder: (context, snapshot) {
          final fontSize = snapshot.data ?? 18.0;
          return Text(
            currentText,
            style: TextStyle(
              fontSize: fontSize,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          );
        },
      ),
    );
  }

  Widget _buildNextButton() {
    final hasChoices = widget.scene.choices?.isNotEmpty ?? false;
    final canAdvance = _hasCompleted && !hasChoices;

    return Center(
      child: IconButton(
        onPressed: canAdvance
            ? widget.onSceneComplete
            : (hasChoices && _hasCompleted)
                ? () {
                    setState(() {
                      _showChoices = true;
                    });
                  }
                : null,
        icon: Icon(
          hasChoices ? Icons.touch_app : Icons.arrow_forward,
          size: 32,
        ),
        style: IconButton.styleFrom(
          backgroundColor: canAdvance || (hasChoices && _hasCompleted)
              ? Colors.green.withValues(alpha: 0.9)
              : Colors.grey.withValues(alpha: 0.5),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.all(12),
        ),
      ),
    );
  }

  Widget _buildProgressRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(_sentences.length, (index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          child: _buildProgressDot(index),
        );
      }),
    );
  }

  Widget _buildProgressDot(int index) {
    final isCompleted = index < _currentSentenceIndex;
    final isCurrent = index == _currentSentenceIndex && _isNarrating;

    double size;
    Color color;

    if (isCurrent) {
      // Large golden dot for current sentence
      size = 16.0;
      color = Colors.amber;
    } else if (isCompleted) {
      // Normal white dot for completed sentences
      size = 12.0;
      color = Colors.white;
    } else {
      // Small dimmed dot for upcoming sentences
      size = 8.0;
      color = Colors.white.withValues(alpha: 0.4);
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        boxShadow: isCurrent
            ? [
                BoxShadow(
                  color: Colors.amber.withValues(alpha: 0.6),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ]
            : null,
      ),
    );
  }

  Widget _buildChoiceOverlay() {
    return Container(
      color: Colors.black54,
      child: Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'What should happen next?',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              ...widget.scene.choices?.map((choice) => _buildChoiceButton(choice)) ?? [],

              const SizedBox(height: 16),

              TextButton(
                onPressed: () {
                  setState(() {
                    _showChoices = false;
                  });
                },
                child: const Text('Cancel'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChoiceButton(ChoiceOptionModel choice) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 12),
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _showChoices = false;
          });
          widget.onChoiceSelected(choice);
        },
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.all(16),
          backgroundColor: Colors.blue[600],
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          choice.option,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
