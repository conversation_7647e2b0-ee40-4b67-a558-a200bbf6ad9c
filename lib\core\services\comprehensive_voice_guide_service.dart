import 'dart:async';
import 'package:choice_once_upon_a_time/core/audio/unified_tts_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';

/// Comprehensive voice guidance service for all user-facing screens
class ComprehensiveVoiceGuideService {
  static final ComprehensiveVoiceGuideService _instance = ComprehensiveVoiceGuideService._internal();
  factory ComprehensiveVoiceGuideService() => _instance;
  static ComprehensiveVoiceGuideService get instance => _instance;
  ComprehensiveVoiceGuideService._internal();

  final UnifiedTTSService _ttsService = UnifiedTTSService.instance;
  final Set<String> _playedGuides = <String>{};
  bool _isPlaying = false;

  /// Play voice guide for story library screen
  Future<void> playStoryLibraryGuide() async {
    const guideKey = 'story_library';
    if (_playedGuides.contains(guideKey)) return;

    const text = '''
Welcome to your story library! Here you can discover magical tales waiting to be told. 
Each story is a unique adventure with beautiful illustrations and engaging characters. 
Tap on any story to begin your journey, or use the search feature to find specific tales.
''';

    await _playGuide(guideKey, text, 'gentle');
  }

  /// Play voice guide for story introduction screen
  Future<void> playStoryIntroGuide(EnhancedStoryModel story) async {
    final guideKey = 'story_intro_${story.storyId}';
    if (_playedGuides.contains(guideKey)) return;

    final text = '''
Welcome to "${story.title}"! This is a wonderful ${story.difficulty} level story set in ${story.setup.setting}.
You'll meet wonderful characters and make important choices that shape the adventure.
The story teaches us about ${story.moral}. Are you ready to begin this magical journey?
''';

    await _playGuide(guideKey, text, 'excited');
  }

  /// Play voice guide for character introduction screen
  Future<void> playCharacterIntroGuide(List<CharacterModel> characters) async {
    const guideKey = 'character_intro';
    if (_playedGuides.contains(guideKey)) return;

    final characterNames = characters.map((c) => c.name).join(', ');
    final text = '''
Let me introduce you to the wonderful characters in this story: $characterNames. 
Each character has their own personality and role to play in the adventure. 
Listen carefully as I describe each character - they'll be your companions throughout the journey!
''';

    await _playGuide(guideKey, text, 'friendly');
  }

  /// Play voice guide for individual character profile
  Future<void> playCharacterProfileGuide(CharacterModel character) async {
    final guideKey = 'character_${character.name}';
    if (_playedGuides.contains(guideKey)) return;

    final text = '''
Meet ${character.name}! ${character.description}
${character.role.isNotEmpty ? 'In this story, ${character.name} is the ${character.role}.' : ''}
Listen carefully to learn more about this important character in our adventure!
''';

    await _playGuide(guideKey, text, 'warm');
  }

  /// Play voice guide for enhanced story player navigation
  Future<void> playStoryPlayerGuide() async {
    const guideKey = 'story_player_nav';
    if (_playedGuides.contains(guideKey)) return;

    const text = '''
Welcome to the story player! You can control the narration using the play, pause, and stop buttons. 
The highlighted text shows what's being read aloud. When choices appear, tap on your preferred option to continue the story. 
You can adjust narration speed and subtitle settings using the settings button.
''';

    await _playGuide(guideKey, text, 'instructional');
  }

  /// Play voice guide for choice selection
  Future<void> playChoiceSelectionGuide(List<ChoiceOptionModel> choices) async {
    const guideKey = 'choice_selection';
    if (_playedGuides.contains(guideKey)) return;

    final choiceCount = choices.length;
    final text = '''
Now it's time to make a choice! You have $choiceCount options to choose from. 
Each choice will lead the story in a different direction. Think carefully about what you want to happen next, 
then tap on your preferred choice to continue the adventure.
''';

    await _playGuide(guideKey, text, 'encouraging');
  }

  /// Play voice guide for story completion
  Future<void> playStoryCompletionGuide(String storyTitle, String moral) async {
    final guideKey = 'story_completion_$storyTitle';
    if (_playedGuides.contains(guideKey)) return;

    final text = '''
Congratulations! You've completed "$storyTitle"! What an amazing adventure that was. 
Remember the important lesson: $moral. 
You can restart this story to explore different choices, or return to the library to discover new tales!
''';

    await _playGuide(guideKey, text, 'celebratory');
  }

  /// Play voice guide for settings screen
  Future<void> playSettingsGuide() async {
    const guideKey = 'settings_screen';
    if (_playedGuides.contains(guideKey)) return;

    const text = '''
Here you can customize your story experience! Adjust the subtitle size to make text easier to read, 
change the narration speed to match your preference, and toggle subtitles on or off. 
These settings will be remembered for all your future stories.
''';

    await _playGuide(guideKey, text, 'helpful');
  }

  /// Play voice guide for parent zone
  Future<void> playParentZoneGuide() async {
    const guideKey = 'parent_zone';
    if (_playedGuides.contains(guideKey)) return;

    const text = '''
Welcome to the Parent Zone! Here you can view your child's reading progress, 
manage their story library, and adjust parental controls. 
You can also see which stories have been completed and track learning achievements.
''';

    await _playGuide(guideKey, text, 'professional');
  }

  /// Manually replay the last played guide
  Future<void> replayLastGuide() async {
    if (_playedGuides.isEmpty) {
      AppLogger.debug('[VOICE_GUIDE] No guides have been played yet');
      return;
    }

    // Get the most recently played guide and replay it
    final lastGuide = _playedGuides.last;
    _playedGuides.remove(lastGuide);
    
    AppLogger.debug('[VOICE_GUIDE] Replaying guide: $lastGuide');
    
    // Re-trigger the appropriate guide based on the key
    if (lastGuide == 'story_library') {
      await playStoryLibraryGuide();
    } else if (lastGuide == 'character_intro') {
      await playCharacterIntroGuide([]);
    } else if (lastGuide == 'story_player_nav') {
      await playStoryPlayerGuide();
    } else if (lastGuide == 'choice_selection') {
      await playChoiceSelectionGuide([]);
    } else if (lastGuide == 'settings_screen') {
      await playSettingsGuide();
    } else if (lastGuide == 'parent_zone') {
      await playParentZoneGuide();
    }
  }

  /// Internal method to play a voice guide
  Future<void> _playGuide(String guideKey, String text, String emotion) async {
    if (_isPlaying) {
      AppLogger.debug('[VOICE_GUIDE] Guide already playing, skipping: $guideKey');
      return;
    }

    try {
      _isPlaying = true;
      AppLogger.debug('[VOICE_GUIDE] Playing guide: $guideKey');

      // Request access to TTS service
      if (!_ttsService.requestAccess('voice_guide')) {
        AppLogger.warning('[VOICE_GUIDE] Cannot access TTS service for guide: $guideKey');
        return;
      }

      // Initialize TTS service if needed
      await _ttsService.initialize();

      // Play the guide text with appropriate emotion
      await _ttsService.speakText(text, emotionCue: emotion);

      // Mark as played
      _playedGuides.add(guideKey);
      AppLogger.debug('[VOICE_GUIDE] Guide completed: $guideKey');

    } catch (e) {
      AppLogger.error('[VOICE_GUIDE] Error playing guide $guideKey', e);
    } finally {
      _isPlaying = false;
      _ttsService.releaseAccess('voice_guide');
    }
  }

  /// Stop current voice guide
  Future<void> stopCurrentGuide() async {
    if (_isPlaying) {
      await _ttsService.stop();
      _ttsService.releaseAccess('voice_guide');
      _isPlaying = false;
      AppLogger.debug('[VOICE_GUIDE] Current guide stopped');
    }
  }

  /// Clear session guides (call when starting new session)
  void clearSessionGuides() {
    _playedGuides.clear();
    AppLogger.debug('[VOICE_GUIDE] Session guides cleared');
  }

  /// Check if a specific guide has been played
  bool hasPlayedGuide(String guideKey) {
    return _playedGuides.contains(guideKey);
  }

  /// Get list of played guides
  List<String> getPlayedGuides() {
    return List.from(_playedGuides);
  }

  /// Check if currently playing a guide
  bool get isPlaying => _isPlaying;
}
