import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/core/storage/offline_storage_service_mobile.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';

/// Widget for displaying a story cover card in the story library
class StoryCoverCardWidget extends ConsumerStatefulWidget {
  final StoryMetadataModel story;
  final VoidCallback? onTap;
  final String languageCode;
  final bool isOneColumn;
  final bool showProgress;

  const StoryCoverCardWidget({
    super.key,
    required this.story,
    required this.isOneColumn,
    this.onTap,
    this.languageCode = 'en-US',
    this.showProgress = false,
  });

  @override
  ConsumerState<StoryCoverCardWidget> createState() => _StoryCoverCardWidgetState();
}

class _StoryCoverCardWidgetState extends ConsumerState<StoryCoverCardWidget> {
  bool _isDownloaded = false;
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  final OfflineStorageService _offlineStorage = OfflineStorageService();

  @override
  void initState() {
    super.initState();
    _checkDownloadStatus();
  }

  Future<void> _checkDownloadStatus() async {
    final isDownloaded = await _offlineStorage.isStoryDownloaded(
      widget.story.id,
      widget.story.version,
    );
    if (mounted) {
      setState(() {
        _isDownloaded = isDownloaded;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: widget.isOneColumn ? 3 : 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: widget.onTap,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Flexible(
              child: AspectRatio(
                aspectRatio: widget.isOneColumn ? 16/9 : 3/2.5,
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    _buildCoverImage(),
                  
                  Positioned(
                    top: widget.isOneColumn ? 12 : 8,
                    right: widget.isOneColumn ? 12 : 8,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        if (widget.story.isNew) 
                          _buildBadge('NEW', Colors.green),
                        if (widget.story.isNew && widget.story.isLocked)
                          SizedBox(height: widget.isOneColumn ? 6 : 4),
                        if (widget.story.isLocked)
                          _buildBadge('LOCKED', Colors.orange),
                      ],
                    ),
                  ),
                  
                  if (widget.story.isLocked)
                    Positioned.fill(
                      child: Container(
                        color: Colors.black.withValues(alpha: 0.3),
                        child: Center(
                          child: Icon(
                            Icons.lock,
                            color: Colors.white,
                            size: widget.isOneColumn ? 40 : 32,
                          ),
                        ),
                      ),
                    ),

                  // Progress indicator for continue reading
                  if (widget.showProgress && widget.story.hasProgress)
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.2),
                        ),
                        child: FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: 0.3, // TODO: Use actual progress from story state
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            ),
            
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: widget.isOneColumn ? 16 : 8,
                vertical: widget.isOneColumn ? 12 : 8,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    widget.story.getLocalizedTitle(widget.languageCode),
                    style: (widget.isOneColumn 
                      ? theme.textTheme.titleLarge 
                      : theme.textTheme.titleMedium
                    )?.copyWith(
                      fontWeight: FontWeight.bold,
                      height: 1.2,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.story.targetMoralValue,
                    style: (widget.isOneColumn 
                      ? theme.textTheme.bodyMedium 
                      : theme.textTheme.bodySmall
                    )?.copyWith(
                      color: theme.colorScheme.secondary,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: widget.isOneColumn ? 8 : 6),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.access_time,
                            size: widget.isOneColumn ? 16 : 14,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${widget.story.estimatedDurationMinutes} min',
                            style: (widget.isOneColumn 
                              ? theme.textTheme.bodyMedium 
                              : theme.textTheme.bodySmall
                            )?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      Text(
                        'Ages ${widget.story.targetAgeSubSegment}',
                        style: (widget.isOneColumn 
                          ? theme.textTheme.bodyMedium 
                          : theme.textTheme.bodySmall
                        )?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  if (!widget.story.isLocked) ...[
                    SizedBox(height: widget.isOneColumn ? 12 : 8),
                    _buildDownloadButton(theme),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBadge(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _buildCoverImage() {
    // Check if this is an asset path (starts with 'assets/')
    if (widget.story.coverImageUrl.startsWith('assets/')) {
      return Image.asset(
        widget.story.coverImageUrl,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildFallbackImage('Asset not found');
        },
      );
    }

    // Check if this is a network URL (starts with 'http')
    if (widget.story.coverImageUrl.startsWith('http')) {
      return Image.network(
        widget.story.coverImageUrl,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildFallbackImage('Network image not available');
        },
      );
    }

    // For any other format, show fallback
    return _buildFallbackImage('Image format not supported');
  }

  Widget _buildFallbackImage(String message) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue[50]!,
            Colors.purple[50]!,
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.book,
            size: 40,
            color: Colors.grey[500],
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              message,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDownloadButton(ThemeData theme) {
    if (_isDownloaded) {
      final isSmallCard = widget.isOneColumn || MediaQuery.of(context).size.width < 600;

      return Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(6),
          child: Container(
            padding: EdgeInsets.symmetric(
              vertical: isSmallCard ? 6 : 8,
              horizontal: isSmallCard ? 8 : 12,
            ),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.offline_pin,
                  size: isSmallCard ? 14 : 16,
                  color: Colors.green.shade600,
                ),
                SizedBox(width: isSmallCard ? 4 : 6),
                Flexible(
                  child: Text(
                    'Read Story',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.green.shade700,
                      fontWeight: FontWeight.w600,
                      fontSize: isSmallCard ? 10 : 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (_isDownloading) {
      final isSmallCard = widget.isOneColumn || MediaQuery.of(context).size.width < 600;

      return Container(
        padding: EdgeInsets.symmetric(
          vertical: isSmallCard ? 6 : 8,
          horizontal: isSmallCard ? 8 : 12,
        ),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: theme.colorScheme.outline.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: isSmallCard ? 12 : 14,
              height: isSmallCard ? 12 : 14,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                value: _downloadProgress,
              ),
            ),
            SizedBox(width: isSmallCard ? 6 : 8),
            Flexible(
              child: Text(
                'Downloading ${(_downloadProgress * 100).round()}%',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w500,
                  fontSize: isSmallCard ? 10 : 12,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      );
    }

    final isSmallCard = widget.isOneColumn || MediaQuery.of(context).size.width < 600;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _downloadStory,
        borderRadius: BorderRadius.circular(6),
        child: Container(
          padding: EdgeInsets.symmetric(
            vertical: isSmallCard ? 6 : 8,
            horizontal: isSmallCard ? 8 : 12,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: theme.colorScheme.primary.withValues(alpha: 0.5)),
            color: theme.colorScheme.primary.withValues(alpha: 0.05),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.download_outlined,
                size: isSmallCard ? 14 : 16,
                color: theme.colorScheme.primary,
              ),
              SizedBox(width: isSmallCard ? 4 : 6),
              Flexible(
                child: Text(
                  'Download',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w600,
                    fontSize: isSmallCard ? 10 : 12,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _downloadStory() async {
    if (_isDownloading || _isDownloaded) return;

    // Check storage permissions first
    final permissionService = ref.read(storagePermissionServiceProvider);
    final hasPermission = await permissionService.handleDownloadPermission(context);
    if (!hasPermission) {
      return; // User denied permission
    }

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    try {
      final success = await _offlineStorage.downloadStory(
        widget.story,
        onProgress: (progress) {
          if (mounted) {
            setState(() {
              _downloadProgress = progress;
            });
          }
        },
        onStatusUpdate: (status) {
          debugPrint('Download status: $status');
        },
      );

      if (mounted) {
        setState(() {
          _isDownloading = false;
          _isDownloaded = success;
          _downloadProgress = success ? 1.0 : 0.0;
        });
      }

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '${widget.story.getLocalizedTitle(widget.languageCode)} downloaded successfully!',
                      style: const TextStyle(color: Colors.white),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.green.shade600,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.white,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Download failed. Please try again.',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.red.shade600,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isDownloading = false;
          _downloadProgress = 0.0;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Download error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
