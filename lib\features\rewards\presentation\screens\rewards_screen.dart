import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/services/story_rewards_service.dart';
import 'package:choice_once_upon_a_time/features/rewards/presentation/widgets/reward_card_widget.dart';
import 'package:choice_once_upon_a_time/features/rewards/presentation/widgets/progress_overview_widget.dart';
import 'package:choice_once_upon_a_time/features/rewards/presentation/widgets/achievement_badge_widget.dart';

/// Screen displaying user's rewards and achievements with animations
class RewardsScreen extends StatefulWidget {
  const RewardsScreen({super.key});

  @override
  State<RewardsScreen> createState() => _RewardsScreenState();
}

class _RewardsScreenState extends State<RewardsScreen>
    with TickerProviderStateMixin {
  late final StoryRewardsService _rewardsService;
  late final AnimationController _headerController;
  late final AnimationController _staggerController;
  late final TabController _tabController;

  late final Animation<double> _headerAnimation;
  late final Animation<Offset> _headerSlideAnimation;

  Map<String, List<String>> _earnedRewards = {};
  Set<String> _completedStories = {};
  Map<String, int> _choiceRewards = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeAnimations();
    _loadRewards();
  }

  void _initializeServices() {
    _rewardsService = StoryRewardsService.instance;
    _tabController = TabController(length: 3, vsync: this);
  }

  void _initializeAnimations() {
    _headerController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _staggerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _headerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _headerController,
      curve: Curves.easeOutBack,
    ));

    _headerSlideAnimation = Tween<Offset>(
      begin: const Offset(0.0, -1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _headerController,
      curve: Curves.easeOutCubic,
    ));
  }

  Future<void> _loadRewards() async {
    try {
      await _rewardsService.initialize();
      
      setState(() {
        _earnedRewards = _rewardsService.earnedRewards;
        _completedStories = _rewardsService.completedStories;
        _choiceRewards = _rewardsService.choiceRewards;
        _isLoading = false;
      });

      // Start animations
      _headerController.forward();
      await Future.delayed(const Duration(milliseconds: 300));
      _staggerController.forward();

    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _headerController.dispose();
    _staggerController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              theme.colorScheme.primary.withValues(alpha: 0.1),
              theme.colorScheme.surface,
              theme.colorScheme.secondary.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Animated header
              SlideTransition(
                position: _headerSlideAnimation,
                child: AnimatedBuilder(
                  animation: _headerAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _headerAnimation.value,
                      child: Opacity(
                        opacity: _headerAnimation.value,
                        child: _buildHeader(theme, isSmallScreen),
                      ),
                    );
                  },
                ),
              ),

              // Tab bar
              if (!_isLoading)
                Container(
                  margin: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 16.0 : 24.0,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: TabBar(
                    controller: _tabController,
                    indicator: BoxDecoration(
                      color: theme.colorScheme.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    labelColor: theme.colorScheme.onPrimary,
                    unselectedLabelColor: theme.colorScheme.onSurfaceVariant,
                    tabs: const [
                      Tab(text: 'Overview'),
                      Tab(text: 'Achievements'),
                      Tab(text: 'Progress'),
                    ],
                  ),
                ),

              const SizedBox(height: 16),

              // Tab content
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : TabBarView(
                        controller: _tabController,
                        children: [
                          _buildOverviewTab(theme, isSmallScreen),
                          _buildAchievementsTab(theme, isSmallScreen),
                          _buildProgressTab(theme, isSmallScreen),
                        ],
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isSmallScreen) {
    final totalRewards = _earnedRewards.values.fold(0, (sum, rewards) => sum + rewards.length);
    final totalStories = _completedStories.length;

    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
      child: Column(
        children: [
          // Back button and title
          Row(
            children: [
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.arrow_back),
              ),
              Expanded(
                child: Text(
                  'Your Rewards',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                    fontSize: isSmallScreen ? 24 : 28,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(width: 48), // Balance the back button
            ],
          ),

          const SizedBox(height: 16),

          // Quick stats
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.emoji_events,
                  value: totalRewards.toString(),
                  label: 'Rewards',
                  color: Colors.amber,
                  theme: theme,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.book,
                  value: totalStories.toString(),
                  label: 'Stories',
                  color: theme.colorScheme.primary,
                  theme: theme,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.star,
                  value: _choiceRewards.values.fold(0, (sum, count) => sum + count).toString(),
                  label: 'Choices',
                  color: Colors.purple,
                  theme: theme,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
    required ThemeData theme,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(ThemeData theme, bool isSmallScreen) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ProgressOverviewWidget(
            completedStories: _completedStories,
            earnedRewards: _earnedRewards,
            choiceRewards: _choiceRewards,
            animationController: _staggerController,
          ),
          
          const SizedBox(height: 24),
          
          Text(
            'Recent Achievements',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Recent rewards from actual data
          ..._buildRecentRewards(theme),
        ],
      ),
    );
  }

  Widget _buildAchievementsTab(ThemeData theme, bool isSmallScreen) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Achievement Badges',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Achievement badges grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: isSmallScreen ? 2 : 3,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.0,
            ),
            itemCount: _getAchievementCount(),
            itemBuilder: (context, index) {
              return AchievementBadgeWidget(
                title: _getAchievementTitle(index),
                description: _getAchievementDescription(index),
                icon: _getAchievementIcon(index),
                isEarned: index < _completedStories.length,
                animationDelay: Duration(milliseconds: index * 150),
                animationController: _staggerController,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildProgressTab(ThemeData theme, bool isSmallScreen) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Story Collection Progress',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Progress indicators for different story categories
          _buildProgressCategory(
            title: 'Kindness Stories',
            completed: 1,
            total: 3,
            color: Colors.pink,
            theme: theme,
          ),
          
          const SizedBox(height: 16),
          
          _buildProgressCategory(
            title: 'Courage Stories',
            completed: 0,
            total: 2,
            color: Colors.orange,
            theme: theme,
          ),
          
          const SizedBox(height: 16),
          
          _buildProgressCategory(
            title: 'Sharing Stories',
            completed: 1,
            total: 2,
            color: Colors.green,
            theme: theme,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressCategory({
    required String title,
    required int completed,
    required int total,
    required Color color,
    required ThemeData theme,
  }) {
    final progress = total > 0 ? completed / total : 0.0;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                '$completed/$total',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          LinearProgressIndicator(
            value: progress,
            backgroundColor: color.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            '${(progress * 100).round()}% Complete',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  String _getAchievementTitle(int index) {
    const titles = [
      'First Story',
      'Kind Heart',
      'Brave Soul',
      'Sharing Spirit',
      'Story Explorer',
      'Choice Master',
    ];
    return titles[index % titles.length];
  }

  String _getAchievementDescription(int index) {
    const descriptions = [
      'Complete your first story',
      'Learn about kindness',
      'Show courage in choices',
      'Practice sharing',
      'Explore different paths',
      'Make thoughtful choices',
    ];
    return descriptions[index % descriptions.length];
  }

  IconData _getAchievementIcon(int index) {
    const icons = [
      Icons.star,
      Icons.favorite,
      Icons.shield,
      Icons.share,
      Icons.explore,
      Icons.psychology,
    ];
    return icons[index % icons.length];
  }

  /// Build recent rewards from actual data
  List<Widget> _buildRecentRewards(ThemeData theme) {
    final recentRewards = <Widget>[];

    // Get recent rewards from earned rewards data
    final allRewards = <RewardEarned>[];
    _earnedRewards.forEach((storyId, rewards) {
      for (final rewardType in rewards) {
        allRewards.add(RewardEarned(
          id: '${storyId}_$rewardType',
          storyId: storyId,
          type: rewardType,
          title: _getRewardTitle(rewardType),
          description: _getRewardDescription(rewardType, storyId),
          earnedAt: DateTime.now().subtract(Duration(days: allRewards.length)),
        ));
      }
    });

    // Sort by earned date (most recent first) and take top 3
    allRewards.sort((a, b) => b.earnedAt.compareTo(a.earnedAt));
    final recentThree = allRewards.take(3).toList();

    if (recentThree.isEmpty) {
      return [
        Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Icon(
                Icons.emoji_events_outlined,
                size: 48,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              const SizedBox(height: 16),
              Text(
                'No rewards yet!',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Complete stories to earn your first rewards',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ];
    }

    for (int i = 0; i < recentThree.length; i++) {
      recentRewards.add(
        Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: RewardCardWidget(
            reward: recentThree[i],
            animationDelay: Duration(milliseconds: i * 200),
            animationController: _staggerController,
          ),
        ),
      );
    }

    return recentRewards;
  }

  /// Get achievement count based on actual data
  int _getAchievementCount() {
    // Base achievements available
    return 6;
  }

  /// Get reward title based on type
  String _getRewardTitle(String rewardType) {
    switch (rewardType) {
      case 'completion':
        return 'Story Complete!';
      case 'choice':
        return 'Great Choice!';
      case 'kindness':
        return 'Kindness Star!';
      case 'sharing':
        return 'Sharing Hero!';
      case 'courage':
        return 'Brave Heart!';
      default:
        return 'Achievement Unlocked!';
    }
  }

  /// Get reward description based on type and story
  String _getRewardDescription(String rewardType, String storyId) {
    switch (rewardType) {
      case 'completion':
        return 'You finished a wonderful story';
      case 'choice':
        return 'You made a thoughtful choice';
      case 'kindness':
        return 'You showed kindness in your story';
      case 'sharing':
        return 'You learned about sharing';
      case 'courage':
        return 'You showed courage in your adventure';
      default:
        return 'You earned a special reward';
    }
  }
}
