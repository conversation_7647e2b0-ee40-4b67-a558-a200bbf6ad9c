├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 UnifiedTTS: Service initialized successfully (Instance: 567561818)
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
Services: TTS Service initialized.
Services: Screen Narration Service initialized with 34 narrations.
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:58:55.989 (+0:00:02.533000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Speech parameters updated - Rate: 0.45, Pitch: 1, Volume: 0.9
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:58:55.993 (+0:00:02.537000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ ⛔ UnifiedTTS Error (User: null, Instance: 567561818): not-allowed
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:58:56.007 (+0:00:02.551000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Speaking text with emotion: gently_encouraging (User: null)
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
FTUE Guide: _playFtueWelcomeGuide started.
FTUE Guide: AppLocalizations not available, exiting.
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:58:59.488 (+0:00:06.032000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Stopped (User: null)
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:58:59.492 (+0:00:06.036000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 VoiceGuide: Stopped playback.
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:58:59.498 (+0:00:06.042000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Speech parameters updated - Rate: 0.45, Pitch: 1, Volume: 0.9
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:58:59.503 (+0:00:06.047000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Speaking text with emotion: gently_encouraging (User: null)
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                                                 log
│ #1   package:logger/src/logger.dart 186:29                                                                  log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [StoryRewardsService] Initializing rewards service
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [StoryRewardsService] Loaded 0 completed stories, 0 reward categories
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [StoryRewardsService] Rewards service initialized successfully
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
FTUE Dispose: Error stopping voice guide: Bad state: Cannot use "ref" after the widget was disposed.
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:00.350 (+0:00:06.894000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Speech started (User: null, Instance: 567561818)
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:02.979 (+0:00:09.523000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Stopped (User: null)
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [StoryRepository] Fetching story metadata from all sources
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [AssetOnlyStoryService] Loading story metadata from assets/stories/stories.json
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:03.115 (+0:00:09.659000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Speech paused (User: null, Instance: 567561818)
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                             log
│ #1   package:logger/src/logger.dart 186:29                                              log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [AssetOnlyStoryService] Loaded 2 story metadata entries
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                                   log
│ #1   package:logger/src/logger.dart 186:29                                                    log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [EnhancedStoryService] Loading all story metadata
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                                   log
│ #1   package:logger/src/logger.dart 186:29                                                    log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [EnhancedStoryService] Scanning for available stories
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [EnhancedStoryService] Found 2 stories: [story001, story002]
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [EnhancedStoryService] Loading story: story001
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [EnhancedStoryService] Successfully loaded story: story001
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [EnhancedStoryService] Loading story: story002
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [EnhancedStoryService] Successfully loaded story: story002
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [EnhancedStoryService] Loaded 2 story metadata entries
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                                   log
│ #1   package:logger/src/logger.dart 186:29                                                    log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [StoryRepository] Total stories available: 2 (2 legacy + 2 enhanced, deduplicated from 4)
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
OfflineStorageService: Error initializing storage: MissingPluginException(No implementation found for method
getApplicationSupportDirectory on channel plugins.flutter.io/path_provider)
OfflineStorageService: Error checking if story is downloaded: StorageException: Failed to initialize storage
Details: MissingPluginException(No implementation found for method getApplicationSupportDirectory on channel     
plugins.flutter.io/path_provider)
OfflineStorageService: Error initializing storage: MissingPluginException(No implementation found for method     
getApplicationSupportDirectory on channel plugins.flutter.io/path_provider)
OfflineStorageService: Error checking if story is downloaded: StorageException: Failed to initialize storage     
Details: MissingPluginException(No implementation found for method getApplicationSupportDirectory on channel     
plugins.flutter.io/path_provider)
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:03.494 (+0:00:10.038000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ ⛔ UnifiedTTS Error (User: null, Instance: 567561818): interrupted
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:05.932 (+0:00:12.476000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Stopped (User: null)
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [StorySettingsService] Initializing settings service
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [StoryRewardsService] Initializing rewards service
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [StoryNarrationService] Initializing TTS service (Instance: 3948302)
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:05.992 (+0:00:12.536000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Access granted to story
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:06.008 (+0:00:12.552000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Service already initialized (Instance: 567561818)
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [StorySettingsService] Settings loaded - Subtitle size: 18, Narration speed: 0.5, Subtitles enabled: true,  
Auto play: true
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [StoryRewardsService] Loaded 0 completed stories, 0 reward categories
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [StorySettingsService] Settings service initialized successfully
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [StoryRewardsService] Rewards service initialized successfully
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:06.063 (+0:00:12.607000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Speech parameters updated - Rate: 0.5, Pitch: 1, Volume: 1
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:06.065 (+0:00:12.609000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Language set to en-US
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [StoryNarrationService] TTS service initialized successfully (Instance: 3948302)
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [EnhancedStoryRepository] Fetching enhanced story: story001
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [EnhancedStoryService] Loading story: story001
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [EnhancedStoryService] Successfully loaded story: story001
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                                            log     
│ #1   package:logger/src/logger.dart 186:29                                                             log     
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [EnhancedStoryRepository] Successfully loaded enhanced story: story001
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:06.098 (+0:00:12.642000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [SCENE_DEBUG] Starting welcome screen voice guide for story: The Lost Toy
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:06.106 (+0:00:12.650000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [VOICE_GUIDE] Playing guide: story_intro_story001
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:06.113 (+0:00:12.657000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ ! UnifiedTTS: Access denied to voice_guide, currently used by story
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:06.120 (+0:00:12.664000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ ! [VOICE_GUIDE] Cannot access TTS service for guide: story_intro_story001
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:06.179 (+0:00:12.723000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [SCENE_DEBUG] Welcome screen voice guide completed
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
Error while trying to load an asset: Flutter Web engine failed to fetch
"assets/assets/stories/story001/images/mia.jpg". HTTP request succeeded, but the server responded with HTTP      
status 404.
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:18.077 (+0:00:24.621000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [SCENE_DEBUG] Starting character introduction voice guide
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:18.079 (+0:00:24.623000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [VOICE_GUIDE] Playing guide: character_intro
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:18.082 (+0:00:24.626000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ ! UnifiedTTS: Access denied to voice_guide, currently used by story
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:18.084 (+0:00:24.628000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ ! [VOICE_GUIDE] Cannot access TTS service for guide: character_intro
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
Error while trying to load an asset: Flutter Web engine failed to fetch
"assets/assets/stories/story001/images/alex.jpg". HTTP request succeeded, but the server responded with HTTP     
status 404.
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:27.431 (+0:00:33.975000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [SCENE_DEBUG] Starting character profile narration for: Alex
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:27.435 (+0:00:33.979000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [VOICE_GUIDE] Playing guide: character_Alex
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:27.439 (+0:00:33.983000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ ! UnifiedTTS: Access denied to voice_guide, currently used by story
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:27.442 (+0:00:33.986000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ ! [VOICE_GUIDE] Cannot access TTS service for guide: character_Alex
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:27.445 (+0:00:33.989000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [SCENE_DEBUG] Character profile narration completed for: Alex
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:37.409 (+0:00:43.953000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [SCENE_DEBUG] Starting character profile narration for: Alex
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:37.416 (+0:00:43.960000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [VOICE_GUIDE] Playing guide: character_Alex
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:37.422 (+0:00:43.966000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ ! UnifiedTTS: Access denied to voice_guide, currently used by story
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:37.428 (+0:00:43.972000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ ! [VOICE_GUIDE] Cannot access TTS service for guide: character_Alex
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 21:59:37.432 (+0:00:43.976000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [SCENE_DEBUG] Character profile narration completed for: Alex
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 22:02:44.209 (+0:03:50.753000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [SCENE_DEBUG] Scene scene_1 initialized - Image: assets/stories/story001/images/park_intro.jpg
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 22:02:44.247 (+0:03:50.791000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [SCENE_DEBUG] Narrating: "Mia and Alex were playing in a sunny park when Ale..."
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 22:02:44.250 (+0:03:50.794000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [SCENE_DEBUG] Narration settings - Speed: 0.5, Emotion: surprised
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 22:02:44.254 (+0:03:50.798000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Speech parameters updated - Rate: 0.5, Pitch: 1, Volume: 1
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 22:02:44.258 (+0:03:50.802000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Language set to en-US
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                             log
│ #1   package:logger/src/logger.dart 186:29                                              log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [StoryNarrationService] Speech rate updated to: 0.5
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 22:02:44.268 (+0:03:50.812000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Speech parameters updated - Rate: 0.5, Pitch: 1, Volume: 1
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 22:02:44.271 (+0:03:50.815000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Language set to en-US
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                             log
│ #1   package:logger/src/logger.dart 186:29                                              log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [StoryNarrationService] Speech rate updated to: 0.5
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 22:02:44.279 (+0:03:50.823000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Speech parameters updated - Rate: 0.5, Pitch: 1.3, Volume: 1
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 22:02:44.283 (+0:03:50.827000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Language set to en-US
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                             log
│ #1   package:logger/src/logger.dart 186:29                                              log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [StoryNarrationService] Speech pitch updated to: 1.3
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 22:02:44.291 (+0:03:50.835000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Speech parameters updated - Rate: 0.5, Pitch: 1.3, Volume: 1
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                            log
│ #1   package:logger/src/logger.dart 186:29                                             log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 22:02:44.295 (+0:03:50.839000)
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 UnifiedTTS: Language set to en-US
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                             log
│ #1   package:logger/src/logger.dart 186:29                                              log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 🐛 [StoryNarrationService] Speech volume updated to: 1
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40
log
│ #1   package:logger/src/logger.dart 186:29
log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ 💡 [StoryNarrationService] Starting narration for scene
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                             log
│ #1   package:logger/src/logger.dart 186:29                                              log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ ! [StoryNarrationService] Could not extract narrator segments: NoSuchMethodError: 'narratorSegments'
│ ! method not found
│ ! Receiver: Instance of '_MockEnhancedScene'
│ ! Arguments: []
└────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
───────
│ #0   package:logger/src/printers/pretty_printer.dart 252:40                             log
│ #1   package:logger/src/logger.dart 186:29                                              log
├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
┄┄┄┄┄┄┄
│ ! [StoryNarrationService] No sentences to narrate in scene