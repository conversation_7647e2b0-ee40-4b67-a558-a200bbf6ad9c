import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/enhanced_narration_widget.dart';

/// Widget for displaying and playing story scenes
class StorySceneWidget extends StatefulWidget {
  final EnhancedStoryModel story;
  final EnhancedSceneModel scene;
  final Function(ChoiceOptionModel) onChoiceSelected;
  final VoidCallback onSceneComplete;
  final StoryNarrationService narrationService;
  final StorySettingsService settingsService;

  const StorySceneWidget({
    super.key,
    required this.story,
    required this.scene,
    required this.onChoiceSelected,
    required this.onSceneComplete,
    required this.narrationService,
    required this.settingsService,
  });

  @override
  State<StorySceneWidget> createState() => _StorySceneWidgetState();
}

class _StorySceneWidgetState extends State<StorySceneWidget>
    with TickerProviderStateMixin {
  late final AnimationController _sceneController;
  late final AnimationController _emotionController;
  late final AnimationController _choicesController;
  
  late final Animation<double> _sceneAnimation;
  late final Animation<double> _emotionAnimation;
  late final Animation<Offset> _choicesSlideAnimation;

  bool _showChoices = false;
  bool _hasNarrated = false;
  bool _narrationComplete = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSceneAnimation();
  }

  void _initializeAnimations() {
    _sceneController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _emotionController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _choicesController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _sceneAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _sceneController,
      curve: Curves.easeInOut,
    ));

    _emotionAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _emotionController,
      curve: Curves.elasticOut,
    ));

    _choicesSlideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _choicesController,
      curve: Curves.easeOutBack,
    ));
  }

  Future<void> _startSceneAnimation() async {
    await _sceneController.forward();
    _triggerEmotionEffect();
  }

  void _triggerEmotionEffect() {
    // Trigger emotion-based visual effect
    switch (widget.scene.emotion.toLowerCase()) {
      case 'happy':
      case 'excited':
      case 'joyful':
        _emotionController.forward().then((_) => _emotionController.reverse());
        break;
      case 'sad':
      case 'disappointed':
        // Could add a different animation for sad emotions
        break;
      case 'surprised':
      case 'amazed':
        _emotionController.repeat(reverse: true, period: const Duration(milliseconds: 800));
        Future.delayed(const Duration(seconds: 2), () {
          _emotionController.stop();
          _emotionController.reset();
        });
        break;
    }
  }

  void _onNarrationComplete() {
    setState(() {
      _hasNarrated = true;
      _narrationComplete = true;
    });

    // Show choices or continue after narration
    if (widget.scene.hasChoices) {
      Future.delayed(const Duration(milliseconds: 500), () {
        _showChoicesWithAnimation();
      });
    } else {
      Future.delayed(const Duration(seconds: 1), () {
        widget.onSceneComplete();
      });
    }
  }

  Future<void> _showChoicesWithAnimation() async {
    setState(() {
      _showChoices = true;
    });
    await _choicesController.forward();
  }

  void _onChoiceSelected(ChoiceOptionModel choice) {
    // Add choice feedback animation
    _choicesController.reverse().then((_) {
      widget.onChoiceSelected(choice);
    });
  }

  Color _getEmotionColor(String emotion) {
    switch (emotion.toLowerCase()) {
      case 'happy':
      case 'excited':
      case 'joyful':
        return Colors.amber;
      case 'sad':
      case 'disappointed':
        return Colors.blue;
      case 'surprised':
      case 'amazed':
        return Colors.purple;
      case 'angry':
        return Colors.red;
      case 'calm':
      case 'peaceful':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  @override
  void dispose() {
    _sceneController.dispose();
    _emotionController.dispose();
    _choicesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            _getEmotionColor(widget.scene.emotion).withValues(alpha: 0.1),
            theme.colorScheme.surface,
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Scene image
            Expanded(
              flex: 3,
              child: AnimatedBuilder(
                animation: _sceneAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _sceneAnimation.value,
                    child: Opacity(
                      opacity: _sceneAnimation.value,
                      child: AnimatedBuilder(
                        animation: _emotionAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _emotionAnimation.value,
                            child: Container(
                              margin: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: _getEmotionColor(widget.scene.emotion).withValues(alpha: 0.3),
                                    blurRadius: 15,
                                    offset: const Offset(0, 5),
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(20),
                                child: Image.asset(
                                  widget.scene.getImagePath(widget.story.storyId),
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      color: theme.colorScheme.primaryContainer,
                                      child: Center(
                                        child: Icon(
                                          Icons.image,
                                          size: isSmallScreen ? 64 : 80,
                                          color: theme.colorScheme.primary,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
            ),

            // Enhanced narration with word-level highlighting
            EnhancedNarrationWidget(
              text: widget.scene.text,
              emotion: widget.scene.emotion,
              narrationService: widget.narrationService,
              settingsService: widget.settingsService,
              onNarrationComplete: _onNarrationComplete,
              autoStart: true,
              emotionColor: _getEmotionColor(widget.scene.emotion),
            ),

            // Choices
            if (_showChoices && widget.scene.hasChoices)
              Expanded(
                flex: 2,
                child: SlideTransition(
                  position: _choicesSlideAnimation,
                  child: Container(
                    padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
                    child: Column(
                      children: [
                        Text(
                          'What should happen next?',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        Expanded(
                          child: ListView.builder(
                            itemCount: widget.scene.choices!.length,
                            itemBuilder: (context, index) {
                              final choice = widget.scene.choices![index];
                              return Container(
                                margin: const EdgeInsets.only(bottom: 12),
                                child: ElevatedButton(
                                  onPressed: () => _onChoiceSelected(choice),
                                  style: ElevatedButton.styleFrom(
                                    padding: const EdgeInsets.all(16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    backgroundColor: theme.colorScheme.primaryContainer,
                                    foregroundColor: theme.colorScheme.onPrimaryContainer,
                                  ),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 40,
                                        height: 40,
                                        decoration: BoxDecoration(
                                          color: theme.colorScheme.primary.withValues(alpha: 0.2),
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        child: Center(
                                          child: Text(
                                            '${index + 1}',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: theme.colorScheme.primary,
                                            ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Text(
                                          choice.option,
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

            // Continue button for non-choice scenes
            if (!widget.scene.hasChoices && _hasNarrated)
              Padding(
                padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: widget.onSceneComplete,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Continue',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
